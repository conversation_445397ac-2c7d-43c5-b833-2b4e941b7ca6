@echo off
echo Starting Airlink Discord Support Bot...
echo.

REM Check if node_modules exists
if not exist "node_modules" (
    echo Installing dependencies...
    npm install
    echo.
)

REM Check if dist directory exists
if not exist "dist" (
    echo Building bot...
    npm run build
    echo.
)

REM Check if .env exists
if not exist ".env" (
    echo .env file not found! Running setup...
    node setup.js
    echo.
    echo Please run this script again after setup is complete.
    pause
    exit /b 1
)

echo Starting bot...
npm start

pause
