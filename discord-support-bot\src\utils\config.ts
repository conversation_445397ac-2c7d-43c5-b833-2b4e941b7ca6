import dotenv from 'dotenv';
import { BotConfig } from '../types';

dotenv.config();

export const config: BotConfig = {
  discordToken: process.env['DISCORD_TOKEN'] || '',
  discordClientId: process.env['DISCORD_CLIENT_ID'] || '',
  aiApiUrl: process.env['AI_API_URL'] || 'https://api.electronhub.ai/v1/chat/completions',
  aiApiKey: process.env['AI_API_KEY'] || 'ek-proxy-R4tBYVMxBMz7cH3dz3yfg5utVNaYuugooC4vQi2hB3hd',
  aiModel: process.env['AI_MODEL'] || 'gpt-4o-mini',
  botPrefix: process.env['BOT_PREFIX'] || '!',
  maxConversationHistory: parseInt(process.env['MAX_CONVERSATION_HISTORY'] || '10'),
  conversationTimeoutMinutes: parseInt(process.env['CONVERSATION_TIMEOUT_MINUTES'] || '30'),
  logLevel: process.env['LOG_LEVEL'] || 'info',
  logFile: process.env['LOG_FILE'] || 'logs/bot.log',
  maxImageSizeMB: parseInt(process.env['MAX_IMAGE_SIZE_MB'] || '15'),
  supportedImageFormats: (process.env['SUPPORTED_IMAGE_FORMATS'] || 'png,jpg,jpeg,gif,webp').split(','),
  dedicatedSupportChannelId: process.env['DEDICATED_SUPPORT_CHANNEL_ID'] || ''
};

export function validateConfig(): void {
  const required = ['discordToken', 'aiApiUrl', 'aiApiKey'];
  const missing = required.filter(key => !config[key as keyof BotConfig]);
  
  if (missing.length > 0) {
    throw new Error(`Missing required configuration: ${missing.join(', ')}`);
  }
}
