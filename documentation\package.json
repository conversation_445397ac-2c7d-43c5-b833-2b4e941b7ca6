{"name": "airlink-docs", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "browserslist": "defaults, not ie <= 11", "dependencies": {"@docsearch/react": "^3.2.1", "@headlessui/react": "^1.7.0", "@markdoc/markdoc": "^0.1.7", "@markdoc/next.js": "^0.1.6", "@sindresorhus/slugify": "^2.1.0", "@tailwindcss/typography": "^0.5.7", "autoprefixer": "^10.4.8", "clsx": "^1.2.1", "focus-visible": "^5.2.0", "next": "^12.3.0", "postcss-focus-visible": "^6.0.4", "postcss-import": "^14.1.0", "prism-react-renderer": "^1.3.5", "react": "18.2.0", "react-dom": "18.2.0", "tailwindcss": "^3.1.8"}, "devDependencies": {"eslint": "8.19.0", "eslint-config-next": "12.2.5", "prettier": "^2.7.1", "prettier-plugin-tailwindcss": "^0.1.13"}}