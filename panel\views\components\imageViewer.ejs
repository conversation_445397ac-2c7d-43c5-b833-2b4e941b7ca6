<!-- Image Viewer Modal Component -->
<div id="imageViewerModal" class="fixed inset-0 bg-black bg-opacity-75 z-50 flex items-center justify-center opacity-0 pointer-events-none transition-opacity duration-300">
    <div class="bg-white dark:bg-neutral-800 rounded-xl p-4 max-w-4xl w-full max-h-[90vh] transform scale-95 transition-transform duration-300 flex flex-col">
        <div class="flex justify-between items-center mb-2">
            <h2 id="imageViewerTitle" class="text-xl font-medium text-neutral-800 dark:text-white truncate"></h2>
            <button onclick="closeImageViewer()" class="text-neutral-500 hover:text-neutral-700 dark:hover:text-white">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
            </button>
        </div>
        <div class="overflow-auto flex-grow flex items-center justify-center p-2">
            <img id="imageViewerContent" class="max-w-full max-h-[calc(90vh-120px)] object-contain" src="" alt="Image preview">
        </div>
        <div class="flex justify-between items-center mt-4">
            <div class="text-sm text-neutral-600 dark:text-neutral-400">
                <span id="imageViewerSize"></span>
            </div>
            <div class="flex gap-2">
                <button id="imageViewerDownload" onclick="downloadViewedImage()" class="px-4 py-2 bg-neutral-100 dark:bg-neutral-700 text-neutral-800 dark:text-white rounded-xl hover:bg-neutral-200 dark:hover:bg-neutral-600 transition">
                    Download
                </button>
                <button onclick="closeImageViewer()" class="px-4 py-2 bg-neutral-800 text-white rounded-xl hover:bg-neutral-700 transition">
                    Close
                </button>
            </div>
        </div>
    </div>
</div>

<script>
    (function() {
        if (window.imageViewerSystem) return;

        window.imageViewerSystem = () => {
            const modal = document.getElementById('imageViewerModal');
            const title = document.getElementById('imageViewerTitle');
            const image = document.getElementById('imageViewerContent');
            const sizeInfo = document.getElementById('imageViewerSize');
            const downloadBtn = document.getElementById('imageViewerDownload');

            let currentImagePath = '';
            let currentFileName = '';
            let currentServerUUID = '';

            const showImageViewer = (fileName, filePath, fileSize, serverUUID) => {
                console.log('Opening image viewer for:', fileName, filePath, fileSize, serverUUID);

                currentImagePath = filePath;
                currentFileName = fileName;
                currentServerUUID = serverUUID;

                title.textContent = fileName;
                sizeInfo.textContent = fileSize;
                image.src = `/server/${serverUUID}/files/download/${encodeURIComponent(filePath)}`;

                modal.classList.remove('opacity-0', 'pointer-events-none');
                modal.querySelector('div').classList.remove('scale-95');
                modal.querySelector('div').classList.add('scale-100');

                document.addEventListener('keydown', handleEscapeKey);
            };

            const closeImageViewer = () => {
                modal.classList.add('opacity-0', 'pointer-events-none');
                modal.querySelector('div').classList.remove('scale-100');
                modal.querySelector('div').classList.add('scale-95');

                document.removeEventListener('keydown', handleEscapeKey);
                setTimeout(() => {
                    image.src = '';
                }, 300);
            };

            const handleEscapeKey = (event) => {
                if (event.key === 'Escape') {
                    closeImageViewer();
                }
            };

            const downloadViewedImage = () => {
                if (currentImagePath && currentFileName && currentServerUUID) {
                    const url = `/server/${currentServerUUID}/files/download/${encodeURIComponent(currentImagePath)}`;
                    fetch(url, { method: 'GET' })
                        .then(response => {
                            if (!response.ok) {
                                throw new Error(`Error downloading file: ${response.statusText}`);
                            }
                            return response.blob();
                        })
                        .then(blob => {
                            const url = window.URL.createObjectURL(blob);
                            const a = document.createElement('a');
                            a.href = url;
                            a.download = currentFileName;
                            document.body.appendChild(a);
                            a.click();
                            document.body.removeChild(a);
                            window.URL.revokeObjectURL(url);
                        })
                        .catch(error => {
                            console.error('Download failed:', error);
                            if (typeof showToast === 'function') {
                                showToast('Failed to download file', 'error');
                            }
                        });
                }
            };

            return { showImageViewer, closeImageViewer, downloadViewedImage };
        };

        const { showImageViewer, closeImageViewer, downloadViewedImage } = window.imageViewerSystem();
        window.showImageViewer = showImageViewer;
        window.closeImageViewer = closeImageViewer;
        window.downloadViewedImage = downloadViewedImage;
    })();
</script>
