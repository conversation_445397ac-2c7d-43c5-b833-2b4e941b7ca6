export interface AIMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

export interface ConversationContext {
  userId: string;
  channelId: string;
  messages: AIMessage[];
  lastActivity: Date;
  topic?: string;
}

export interface AIResponse {
  content: string;
  usage?: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

export interface ImageAnalysisResult {
  extractedText: string;
  isError: boolean;
  errorType?: string | undefined;
  suggestions: string[];
}

export interface AirlinkKnowledge {
  category: string;
  title: string;
  content: string;
  keywords: string[];
  relatedTopics: string[];
}

export interface BotConfig {
  discordToken: string;
  discordClientId: string;
  aiApiUrl: string;
  aiApiKey: string;
  aiModel: string;
  botPrefix: string;
  maxConversationHistory: number;
  conversationTimeoutMinutes: number;
  logLevel: string;
  logFile: string;
  maxImageSizeMB: number;
  supportedImageFormats: string[];
  dedicatedSupportChannelId: string;
}

export interface ErrorPattern {
  pattern: RegExp;
  type: string;
  category: string;
  commonCauses: string[];
  solutions: string[];
}
