import { Client, GatewayIntentBits, Events, Message, EmbedBuilder } from 'discord.js';
import { config, validateConfig } from './utils/config';
import logger from './utils/logger';
import { AIService } from './services/aiService';
import { ConversationManager } from './services/conversationManager';
import { KnowledgeBase } from './services/knowledgeBase';
import { ImageProcessor } from './services/imageProcessor';
import { AIMessage } from './types';

class AirlinkSupportBot {
  private client: Client;
  private aiService: AIService;
  private conversationManager: ConversationManager;
  private knowledgeBase: KnowledgeBase;
  private imageProcessor: ImageProcessor;

  constructor() {
    this.client = new Client({
      intents: [
        GatewayIntentBits.Guilds,
        GatewayIntentBits.GuildMessages,
        GatewayIntentBits.MessageContent,
        GatewayIntentBits.DirectMessages
      ]
    });

    this.aiService = new AIService();
    this.conversationManager = new ConversationManager();
    this.knowledgeBase = new KnowledgeBase();
    this.imageProcessor = new ImageProcessor();

    this.setupEventHandlers();
  }

  private setupEventHandlers(): void {
    this.client.once(Events.ClientReady, () => {
      logger.info(`✅ Bot is ready! Logged in as ${this.client.user?.tag}`);
      this.client.user?.setActivity('Helping with Airlink Panel', { type: 3 });

      if (config.dedicatedSupportChannelId) {
        logger.info(`🎯 Dedicated support channel configured: ${config.dedicatedSupportChannelId}`);
        logger.info('📢 Bot will create support threads when @mentioned in the dedicated channel');
        logger.info('🧵 Bot responds automatically to all messages in support threads');
      } else {
        logger.info('💡 No dedicated support channel configured. Bot will respond to mentions, DMs, and channels with "support" in the name');
      }
    });

    this.client.on(Events.MessageCreate, async (message: Message) => {
      await this.handleMessage(message);
    });

    this.client.on(Events.Error, (error) => {
      logger.error('Discord client error:', error);
    });

    process.on('SIGINT', () => {
      logger.info('Received SIGINT, shutting down gracefully...');
      this.shutdown();
    });

    process.on('SIGTERM', () => {
      logger.info('Received SIGTERM, shutting down gracefully...');
      this.shutdown();
    });
  }

  private async handleMessage(message: Message): Promise<void> {
    try {
      // Ignore bot messages
      if (message.author.bot) return;

      // Check if bot should respond
      const shouldRespond = this.shouldRespondToMessage(message);
      if (!shouldRespond) return;

      // Show typing indicator
      if ('sendTyping' in message.channel) {
        await message.channel.sendTyping();
      }

      // Handle image attachments
      if (message.attachments.size > 0) {
        await this.handleImageMessage(message);
        return;
      }

      // Handle text message
      await this.handleTextMessage(message);
    } catch (error) {
      logger.error('Error handling message:', error);
      await this.sendErrorResponse(message);
    }
  }

  private shouldRespondToMessage(message: Message): boolean {
    // Respond to DMs
    if (message.channel.type === 1) return true;

    // Respond to ALL messages in threads created by the bot (support threads)
    if (message.channel.type === 11 && message.channel.name.startsWith('🎫')) return true;

    // For all other channels (including support channels), only respond to mentions or prefix commands
    if (message.mentions.has(this.client.user!)) return true;
    if (message.content.startsWith(config.botPrefix)) return true;

    return false;
  }

  private async handleTextMessage(message: Message): Promise<void> {
    const userId = message.author.id;
    let channelId = message.channel.id;
    const userMessage = message.content.replace(`<@${this.client.user?.id}>`, '').trim();

    // Check if this is a mention in the dedicated support channel - create thread
    let responseChannel = message.channel;
    if (config.dedicatedSupportChannelId &&
        message.channel.id === config.dedicatedSupportChannelId &&
        message.mentions.has(this.client.user!) &&
        message.channel.type === 0) {

      try {
        // Create a thread for this support request
        const thread = await message.startThread({
          name: `🎫 ${message.author.username}'s Support`,
          autoArchiveDuration: 1440, // 24 hours
          reason: 'Support ticket created'
        });

        responseChannel = thread;
        channelId = thread.id;

        logger.info(`Created support thread ${thread.id} for user ${userId}`);
      } catch (error) {
        logger.error('Failed to create thread:', error);
        // Continue with normal response in the original channel
      }
    }

    // Add user message to conversation
    this.conversationManager.addMessage(userId, channelId, {
      role: 'user',
      content: userMessage
    });

    // Get conversation history
    const conversationHistory = this.conversationManager.getConversationHistory(userId, channelId);

    // Search knowledge base for relevant solution
    const knowledgeResult = this.knowledgeBase.searchKnowledgeBase(userMessage);
    let contextInfo = '';

    if (knowledgeResult) {
      contextInfo = `\n\n**Found Relevant Solution:**\n${knowledgeResult}\n\nUse this information to help answer the user's question.`;
    }

    // Prepare messages for AI
    const messages: AIMessage[] = [
      {
        role: 'system',
        content: this.knowledgeBase.getSystemPrompt() + contextInfo
      },
      ...conversationHistory
    ];

    // Check for admin commands
    if (await this.handleAdminCommands(message, userMessage)) {
      return;
    }

    // Check for error patterns in the message
    const errorAnalysis = this.knowledgeBase.analyzeError(userMessage);
    if (errorAnalysis) {
      await this.sendErrorAnalysisResponseToChannel(responseChannel, errorAnalysis);
      return;
    }

    // Generate AI response
    const aiResponse = await this.aiService.generateResponse(messages);

    // Filter response to prevent @everyone pings
    const filteredResponse = this.filterMentions(aiResponse.content);

    // Add AI response to conversation
    this.conversationManager.addMessage(userId, channelId, {
      role: 'assistant',
      content: filteredResponse
    });

    // Send response to the appropriate channel (thread or original)
    await this.sendResponseToChannel(responseChannel, filteredResponse);
  }

  private async handleImageMessage(message: Message): Promise<void> {
    const userId = message.author.id;
    const channelId = message.channel.id;
    const userText = message.content || 'Please analyze this image';

    for (const attachment of message.attachments.values()) {
      const validation = this.imageProcessor.validateImageForProcessing(attachment);
      
      if (!validation.valid) {
        await message.reply(`❌ ${validation.reason}`);
        continue;
      }

      try {
        // Process image
        const imageUrl = await this.imageProcessor.processImageForAnalysis(attachment);
        
        // Analyze image with AI
        const analysis = await this.aiService.analyzeImage(imageUrl, userText);

        // Filter analysis results to prevent @everyone pings
        const filteredExtractedText = this.filterMentions(analysis.extractedText || 'No text detected');
        const filteredErrorType = analysis.errorType ? this.filterMentions(analysis.errorType) : undefined;
        const filteredSuggestions = analysis.suggestions.map(s => this.filterMentions(s));

        // Create response embed
        const embed = new EmbedBuilder()
          .setTitle('🔍 Image Analysis Results')
          .setColor(analysis.isError ? 0xff6b6b : 0x4ecdc4)
          .addFields(
            {
              name: '📝 Extracted Text',
              value: filteredExtractedText,
              inline: false
            }
          );

        if (filteredErrorType) {
          embed.addFields({
            name: '⚠️ Error Type',
            value: filteredErrorType,
            inline: true
          });
        }

        if (filteredSuggestions.length > 0) {
          embed.addFields({
            name: '💡 Suggestions',
            value: filteredSuggestions.map((s, i) => `${i + 1}. ${s}`).join('\n'),
            inline: false
          });
        }

        await message.reply({ embeds: [embed] });

        // Add to conversation history
        this.conversationManager.addMessage(userId, channelId, {
          role: 'user',
          content: `[Image uploaded] ${userText}`
        });

        this.conversationManager.addMessage(userId, channelId, {
          role: 'assistant',
          content: `Analyzed image: ${filteredExtractedText}. ${filteredSuggestions.join(' ')}`
        });

      } catch (error) {
        logger.error('Image analysis error:', error);
        await message.reply('❌ Failed to analyze image. Please try again or describe the error in text.');
      }
    }
  }



  private async sendResponseToChannel(channel: any, content: string): Promise<void> {
    // Split long messages
    if (content.length > 2000) {
      const chunks = this.splitMessage(content, 2000);
      for (const chunk of chunks) {
        await channel.send(chunk);
      }
    } else {
      await channel.send(content);
    }
  }

  private async sendErrorAnalysisResponseToChannel(channel: any, errorAnalysis: any): Promise<void> {
    // Filter error analysis content to prevent @everyone pings
    const filteredType = this.filterMentions(errorAnalysis.type);
    const filteredCauses = errorAnalysis.commonCauses.map((c: string) => this.filterMentions(c));
    const filteredSolutions = errorAnalysis.solutions.map((s: string) => this.filterMentions(s));

    const embed = new EmbedBuilder()
      .setTitle(`🚨 ${filteredType} Detected`)
      .setColor(0xff6b6b)
      .addFields(
        {
          name: '🔍 Common Causes',
          value: filteredCauses.map((c: string, i: number) => `${i + 1}. ${c}`).join('\n'),
          inline: false
        },
        {
          name: '🛠️ Solutions',
          value: filteredSolutions.map((s: string, i: number) => `${i + 1}. ${s}`).join('\n'),
          inline: false
        }
      )
      .setFooter({ text: 'Need more help? Feel free to ask follow-up questions!' });

    await channel.send({ embeds: [embed] });
  }

  private async sendErrorResponse(message: Message): Promise<void> {
    await message.reply('❌ Sorry, I encountered an error while processing your request. Please try again.');
  }

  private splitMessage(text: string, maxLength: number): string[] {
    const chunks: string[] = [];
    let currentChunk = '';

    const lines = text.split('\n');
    for (const line of lines) {
      if (currentChunk.length + line.length + 1 > maxLength) {
        if (currentChunk) {
          chunks.push(currentChunk.trim());
          currentChunk = '';
        }
      }
      currentChunk += line + '\n';
    }

    if (currentChunk.trim()) {
      chunks.push(currentChunk.trim());
    }

    return chunks;
  }

  private async handleAdminCommands(message: Message, userMessage: string): Promise<boolean> {
    // Only allow admin commands from server administrators
    if (message.guild && !message.member?.permissions.has('Administrator')) {
      return false;
    }

    const command = userMessage.toLowerCase().trim();

    if (command.startsWith('!kb add ')) {
      const content = userMessage.substring(8).trim();
      const lines = content.split('\n');
      if (lines.length < 3) {
        await message.reply('❌ Format: `!kb add <problem>\n<keywords>\n<solution>`');
        return true;
      }

      const [problem, keywordsStr, ...solutionLines] = lines;
      const solution = solutionLines.join('\n');

      if (!problem || !keywordsStr || !solution) {
        await message.reply('❌ Problem, keywords, and solution are required');
        return true;
      }

      const keywords = keywordsStr.split(',').map(k => k.trim());
      this.knowledgeBase.addToKnowledgeBase(problem, keywords, solution);
      await message.reply(`✅ Added "${problem}" to knowledge base`);
      return true;
    }

    if (command === '!kb status') {
      const stats = this.knowledgeBase.getKnowledgeBaseStats();
      await message.reply(`📊 Knowledge Base: ${stats.count} entries`);
      return true;
    }

    if (command.startsWith('!kb search ')) {
      const query = userMessage.substring(11).trim();
      const result = this.knowledgeBase.searchKnowledgeBase(query);

      if (!result) {
        await message.reply('❌ No matching problem found');
      } else {
        await message.reply(`🔍 **Knowledge Base Result:**\n\n${result}`);
      }
      return true;
    }

    if (command === '!kb reload') {
      this.knowledgeBase.reloadKnowledgeBase();
      await message.reply('✅ Knowledge base reloaded from file');
      return true;
    }

    return false;
  }

  private filterMentions(text: string): string {
    // Replace @everyone with "everyone" to prevent mass pings
    return text
      .replace(/@everyone/g, 'everyone')
      .replace(/@here/g, 'here');
  }

  async start(): Promise<void> {
    try {
      validateConfig();
      await this.client.login(config.discordToken);
      logger.info('🚀 Airlink Support Bot started successfully');
    } catch (error) {
      logger.error('Failed to start bot:', error);
      process.exit(1);
    }
  }

  private shutdown(): void {
    logger.info('Shutting down bot...');
    this.conversationManager.destroy();
    this.client.destroy();
    process.exit(0);
  }
}

// Start the bot
const bot = new AirlinkSupportBot();
bot.start().catch(error => {
  logger.error('Failed to start bot:', error);
  process.exit(1);
});
