<%- include('../components/header', { title: 'Login'}) %>
<div class="min-h-full flex">
  <div class="flex-1 flex flex-col justify-center py-12 px-4 sm:px-6 lg:flex-none lg:px-20 xl:px-24 border-r border-neutral-950/20">
    <div class="mx-auto w-full max-w-sm lg:w-96">
      <div>
        <% if (settings.logo) { %><img class="h-12 w-auto rounded-xl" src="<%= settings.logo %>"> <% } %>
        <h2 class="<% if (settings.logo == true) { %>mt-6<% } %> text-3xl font-medium text-neutral-800 dark:text-white">Sign in to <%= settings.title %></h2>
        <p class="mt-2 text-sm font-normal text-neutral-500">
          Please enter your account credentials below.
        </p>
      </div>

      <div class="mt-8">
        <div class="mt-6">
          <form id="login-form" class="space-y-6" action="/login" method="POST">
            <%- include('../components/csrf') %>
            <div>
              <label for="email" class="block text-sm font-medium text-neutral-600 dark:text-neutral-400"> Username or Email </label>
              <div class="mt-2">
                <input name="identifier" placeholder="example" id="username" autocomplete="username" required class="appearance-none block w-full px-3 py-2 bg-white dark:bg-white/10 border border-neutral-300 dark:border-neutral-800/10 rounded-xl placeholder-neutral-400 dark:placeholder-neutral-500 focus:outline-none focus:ring-neutral-500 focus:border-neutral-500 text-neutral-800 dark:text-white transition sm:text-sm">
              </div>
            </div>

            <div class="space-y-1">
              <label for="password" class="block text-sm font-medium text-neutral-600 dark:text-neutral-400"> Password </label>
              <div class="mt-2">
                <input id="password" name="password" placeholder="********" type="password" autocomplete="current-password" required class="appearance-none block w-full px-3 py-2 bg-white dark:bg-white/10 border border-neutral-300 dark:border-neutral-800/10 rounded-xl placeholder-neutral-400 dark:placeholder-neutral-500 focus:outline-none focus:ring-neutral-500 focus:border-neutral-500 text-neutral-800 dark:text-white transition sm:text-sm">
              </div>
            </div>

            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <input id="remember-me" name="remember-me" type="checkbox" class="h-4 w-4 text-neutral-600 focus:ring-neutral-500 border-neutral-300 bg-white dark:border-white/10 dark:bg-white/5 rounded">
                <label for="remember-me" class="ml-2 block text-sm text-neutral-600 dark:text-neutral-400"> Remember me </label>
              </div>

              <div class="text-sm">
                <a href="/auth/reset-password" class="font-normal text-neutral-500 hover:text-neutral-800 dark:hover:text-white transition">Forgot your password?</a>
              </div>
            </div>

            <div>
              <button type="submit" class="w-full flex transition justify-center py-2 px-4 border border-transparent rounded-xl shadow-sm text-sm font-medium text-neutral-800 bg-white hover:bg-neutral-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-neutral-500">Sign in</button>
            </div>

            <% if (settings.register) { %>
            <div class="mt-6 text-sm text-neutral-500 py-[-50px]">
              Need an account? <a href="/register" class="font-normal text-neutral-500 hover:text-neutral-800 dark:hover:text-white transition">Register</a>
            </div>
            <% } %>

            <% if (req.query.err && req.query.err == "missing_credentials") { %>
              <div class="mt-2"></div>
              <span class="text-amber-600 pt-4 font-normal text-sm">Missing credentials. Please try again.</span>
            <% } %>

            <% if (req.query.err && req.query.err == "user_not_found") { %>
                <div class="mt-2"></div>
                <span class="text-amber-600 pt-4 font-normal text-sm">User not found. Please try again.</span>
            <% } %>

            <% if (req.query.err && req.query.err == "incorrect_password") { %>
                <div class="mt-2"></div>
                <span class="text-amber-600 pt-4 font-normal text-sm">Incorrect password. Please try again.</span>
            <% } %>

            <% if (req.query.err && req.query.err == "database_error") { %>
                <div class="mt-2"></div>
                <span class="text-amber-600 pt-4 font-normal text-sm">Database error. Please try again.</span>
            <% } %>

          </form>
        </div>
      </div>
    </div>
  </div>
  <div class="hidden lg:block relative w-0 flex-1">
    <img class="absolute inset-0 h-full w-full object-cover" src="https://i.imgur.com/j9BodUY.jpeg" alt="">
  </div>
</div>
<%- include('../components/footer') %>