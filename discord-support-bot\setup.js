#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function question(prompt) {
  return new Promise((resolve) => {
    rl.question(prompt, resolve);
  });
}

async function setup() {
  console.log('🤖 Airlink Discord Support Bot Setup\n');
  
  // Check if .env already exists
  const envPath = path.join(__dirname, '.env');
  if (fs.existsSync(envPath)) {
    const overwrite = await question('⚠️  .env file already exists. Overwrite? (y/N): ');
    if (overwrite.toLowerCase() !== 'y') {
      console.log('Setup cancelled.');
      rl.close();
      return;
    }
  }

  console.log('Please provide the following information:\n');

  // Discord configuration
  const discordToken = await question('🔑 Discord Bot Token: ');
  if (!discordToken.trim()) {
    console.log('❌ Discord Bot Token is required!');
    rl.close();
    return;
  }

  const discordClientId = await question('🆔 Discord Client ID: ');
  if (!discordClientId.trim()) {
    console.log('❌ Discord Client ID is required!');
    rl.close();
    return;
  }

  const dedicatedChannelId = await question('📢 Dedicated Support Channel ID (optional - bot will listen to ALL messages in this channel): ');
  console.log('💡 Tip: Right-click a channel in Discord → Copy Channel ID to get the ID');

  // AI configuration (pre-filled with your values)
  console.log('\n🤖 AI Configuration (using your custom API):');
  const aiApiUrl = 'https://api.electronhub.ai/v1/chat/completions';
  const aiApiKey = 'ek-proxy-R4tBYVMxBMz7cH3dz3yfg5utVNaYuugooC4vQi2hB3hd';
  const aiModel = 'gpt-4o-mini';
  
  console.log(`✅ API URL: ${aiApiUrl}`);
  console.log(`✅ API Key: ${aiApiKey.substring(0, 20)}...`);
  console.log(`✅ Model: ${aiModel}`);

  // Optional configuration
  console.log('\n⚙️  Optional Configuration (press Enter for defaults):');
  
  const botPrefix = await question('Bot Prefix (!): ') || '!';
  const maxHistory = await question('Max Conversation History (10): ') || '10';
  const timeoutMinutes = await question('Conversation Timeout Minutes (30): ') || '30';
  const logLevel = await question('Log Level (info): ') || 'info';
  const maxImageSize = await question('Max Image Size MB (15): ') || '15';

  // Create .env content
  const envContent = `# Discord Bot Configuration
DISCORD_TOKEN=${discordToken}
DISCORD_CLIENT_ID=${discordClientId}
DEDICATED_SUPPORT_CHANNEL_ID=${dedicatedChannelId}

# AI Configuration
AI_API_URL=${aiApiUrl}
AI_API_KEY=${aiApiKey}
AI_MODEL=${aiModel}

# Bot Configuration
BOT_PREFIX=${botPrefix}
MAX_CONVERSATION_HISTORY=${maxHistory}
CONVERSATION_TIMEOUT_MINUTES=${timeoutMinutes}

# Logging
LOG_LEVEL=${logLevel}
LOG_FILE=logs/bot.log

# Image processing settings
MAX_IMAGE_SIZE_MB=${maxImageSize}
SUPPORTED_IMAGE_FORMATS=png,jpg,jpeg,gif,webp
`;

  // Write .env file
  try {
    fs.writeFileSync(envPath, envContent);
    console.log('\n✅ .env file created successfully!');
  } catch (error) {
    console.log('❌ Failed to create .env file:', error.message);
    rl.close();
    return;
  }

  // Create logs directory
  const logsDir = path.join(__dirname, 'logs');
  if (!fs.existsSync(logsDir)) {
    try {
      fs.mkdirSync(logsDir);
      console.log('✅ Logs directory created');
    } catch (error) {
      console.log('⚠️  Could not create logs directory:', error.message);
    }
  }

  console.log('\n🎉 Setup complete!');
  console.log('\nNext steps:');
  console.log('1. Install dependencies: npm install');
  console.log('2. Build the bot: npm run build');
  console.log('3. Start the bot: npm start');
  console.log('\nFor development: npm run dev');
  console.log('\n📚 Check README.md for Discord bot setup instructions.');

  rl.close();
}

setup().catch(error => {
  console.error('Setup failed:', error);
  rl.close();
  process.exit(1);
});
