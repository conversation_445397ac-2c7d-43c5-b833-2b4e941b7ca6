{"name": "airlink-discord-support-bot", "version": "1.0.0", "description": "AI-powered Discord support bot for Airlink Panel", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "ts-node-dev --respawn --transpile-only src/index.ts", "setup": "node setup.js", "lint": "eslint src/**/*.ts", "format": "prettier --write src/**/*.ts", "clean": "<PERSON><PERSON><PERSON> dist", "prebuild": "npm run clean"}, "keywords": ["discord", "bot", "airlink", "support", "ai", "gpt-4o-mini"], "author": "Airlink Labs", "license": "MIT", "dependencies": {"discord.js": "^14.14.1", "axios": "^1.6.7", "dotenv": "^16.4.1", "sharp": "^0.33.2", "node-schedule": "^2.1.1", "winston": "^3.11.0"}, "devDependencies": {"@types/node": "^20.11.16", "@types/node-schedule": "^2.1.6", "typescript": "^5.3.3", "ts-node-dev": "^2.0.0", "eslint": "^8.56.0", "@typescript-eslint/eslint-plugin": "^6.20.0", "@typescript-eslint/parser": "^6.20.0", "prettier": "^3.2.5", "rimraf": "^5.0.5"}, "engines": {"node": ">=18.0.0"}}