import { Attachment } from 'discord.js';
import sharp from 'sharp';
import { config } from '../utils/config';
import logger from '../utils/logger';

export class ImageProcessor {
  isValidImageAttachment(attachment: Attachment): boolean {
    if (!attachment.contentType) return false;
    
    const isImage = attachment.contentType.startsWith('image/');
    const hasValidFormat = config.supportedImageFormats.some(format => 
      attachment.contentType!.includes(format)
    );
    const isValidSize = attachment.size <= config.maxImageSizeMB * 1024 * 1024;

    return isImage && hasValidFormat && isValidSize;
  }

  async processImageForAnalysis(attachment: Attachment): Promise<string> {
    try {
      if (!this.isValidImageAttachment(attachment)) {
        throw new Error('Invalid image format or size');
      }

      // For AI analysis, we can directly use the Discord CDN URL
      // The AI service will fetch and analyze the image
      return attachment.url;
    } catch (error) {
      logger.error('Image processing error:', error);
      throw new Error('Failed to process image for analysis');
    }
  }

  async optimizeImage(imageBuffer: Buffer): Promise<Buffer> {
    try {
      // Optimize image for better OCR results
      return await sharp(imageBuffer)
        .resize(2000, 2000, { 
          fit: 'inside', 
          withoutEnlargement: true 
        })
        .sharpen()
        .normalize()
        .png({ quality: 90 })
        .toBuffer();
    } catch (error) {
      logger.error('Image optimization error:', error);
      return imageBuffer; // Return original if optimization fails
    }
  }

  getImageInfo(attachment: Attachment): string {
    const sizeInMB = (attachment.size / (1024 * 1024)).toFixed(2);
    return `📸 **Image Info:**
- **Size:** ${sizeInMB} MB
- **Format:** ${attachment.contentType}
- **Dimensions:** ${attachment.width}x${attachment.height}`;
  }

  validateImageForProcessing(attachment: Attachment): { valid: boolean; reason?: string } {
    if (!attachment.contentType?.startsWith('image/')) {
      return { valid: false, reason: 'File is not an image' };
    }

    if (!config.supportedImageFormats.some(format => 
      attachment.contentType!.includes(format)
    )) {
      return { 
        valid: false, 
        reason: `Unsupported format. Supported formats: ${config.supportedImageFormats.join(', ')}` 
      };
    }

    if (attachment.size > config.maxImageSizeMB * 1024 * 1024) {
      return { 
        valid: false, 
        reason: `Image too large. Maximum size: ${config.maxImageSizeMB}MB` 
      };
    }

    return { valid: true };
  }
}
