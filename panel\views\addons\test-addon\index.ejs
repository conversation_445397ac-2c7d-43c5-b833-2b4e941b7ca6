<%- include('../../components/header', { title: 'Test Addon' }) %>

<main class="h-screen m-auto">
  <div class="flex h-screen">
    <!-- Sidebar -->
    <div class="w-60 h-full">
      <%- include('../../components/template') %>
    </div>
    <!-- Content -->
    <div class="flex-1 p-6 overflow-y-auto pt-16">
      <div class="sm:flex sm:items-center px-8 pt-4">
        <div class="sm:flex-auto">
           <h1 class="text-base font-medium leading-6 text-white">Test Addon</h1>
           <p class="mt-1 tracking-tight text-sm text-neutral-500">This is a test addon for AirLink Panel</p>
         </div>
       </div>
       <div class="px-8 mt-5">
        <div class="rounded-xl bg-neutral-900 p-6">
          <div class="sm:flex sm:items-center">
            <div class="sm:flex-auto">
              <h1 class="text-base font-semibold leading-6 text-white">System Statistics</h1>
              <p class="mt-2 text-sm text-neutral-400">This addon demonstrates how to access the database and display information.</p>
            </div>
          </div>
          <div class="mt-8 flow-root">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div class="bg-neutral-800 rounded-lg p-4">
                <h3 class="text-lg font-medium text-white">Users</h3>
                <p class="text-3xl font-bold text-white mt-2"><%= userCount %></p>
                <p class="text-sm text-neutral-400 mt-1">Total registered users</p>
              </div>
              <div class="bg-neutral-800 rounded-lg p-4">
                <h3 class="text-lg font-medium text-white">Servers</h3>
                <p class="text-3xl font-bold text-white mt-2"><%= serverCount %></p>
                <p class="text-sm text-neutral-400 mt-1">Total created servers</p>
              </div>
            </div>
            <div class="mt-6">
              <h3 class="text-lg font-medium text-white mb-4">API Example</h3>
              <p class="text-sm text-neutral-400 mb-2">This addon also provides an API endpoint at <code>/test-addon/api/stats</code></p>
              <button id="fetchApiBtn" class="w-full md:w-auto rounded-xl bg-neutral-950 dark:bg-white hover:bg-neutral-300 text-neutral-200 dark:text-neutral-800 px-3 py-2 text-sm font-medium shadow-md transition focus:outline focus:outline-2 focus:outline-offset-2">
                Fetch API Data
              </button>
              <pre id="apiResult" class="mt-4 bg-neutral-800 p-4 rounded-lg text-neutral-300 text-sm hidden"></pre>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</main>

<script>
  document.getElementById('fetchApiBtn').addEventListener('click', async () => {
    try {
      const response = await fetch('/test-addon/api/stats');
      const data = await response.json();
      
      const resultElement = document.getElementById('apiResult');
      resultElement.textContent = JSON.stringify(data, null, 2);
      resultElement.classList.remove('hidden');
    } catch (error) {
      console.error('Error fetching API data:', error);
      showToast('error', 'Failed to fetch API data');
    }
  });
</script>

<%- include('../../components/footer') %>
