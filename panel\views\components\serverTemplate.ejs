<div class="mt-6 ml-8 mb-8">
    <div>
       <div class="hidden relative sm:block">
          <nav class="flex relative">
            <div id="sliding-background" class="absolute w-fit h-9 px-3 transition-transform duration-200 ease-in-out z-0 bg-neutral-200 border border-neutral-300 dark:bg-white/5 dark:border-neutral-300/5 rounded-xl"></div>
             <ul role="list" class="flex min-w-full mt-1.5 flex-none gap-x-2 text-sm font-normal leading-6 text-neutral-600 dark:text-neutral-400">
                <%
                const menuItems = global.uiComponentStore ? global.uiComponentStore.getServerMenuItems() : [];
                if (!menuItems || menuItems.length === 0) {
                %>
                <li class="transition">
                   <a href="/server/<%= server.UUID %>" class="nav-link2 py-2 px-3 transition border hover:bg-neutral-100 dark:hover:bg-white/5 border-transparent hover:text-neutral-900 dark:hover:text-white hover:shadow rounded-xl">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="size-5 mb-0.5 inline-flex mr-1">
                      <path fill-rule="evenodd" d="M2.25 6a3 3 0 0 1 3-3h13.5a3 3 0 0 1 3 3v12a3 3 0 0 1-3 3H5.25a3 3 0 0 1-3-3V6Zm3.97.97a.75.75 0 0 1 1.06 0l2.25 2.25a.75.75 0 0 1 0 1.06l-2.25 2.25a.75.75 0 0 1-1.06-1.06l1.72-1.72-1.72-1.72a.75.75 0 0 1 0-1.06Zm4.28 4.28a.75.75 0 0 0 0 1.5h3a.75.75 0 0 0 0-1.5h-3Z" clip-rule="evenodd" />
                    </svg>
                     Console
                  </a>
                </li>
                <li class="transition">
                   <a href="/server/<%= server.UUID %>/files" class="nav-link2 py-2 px-3 transition border hover:bg-neutral-100 dark:hover:bg-white/5 border-transparent hover:text-neutral-900 dark:hover:text-white hover:shadow rounded-xl">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="size-5 mb-0.5 inline-flex mr-1">
                      <path d="M19.906 9c.382 0 .749.057 1.094.162V9a3 3 0 0 0-3-3h-3.879a.75.75 0 0 1-.53-.22L11.47 3.66A2.25 2.25 0 0 0 9.879 3H6a3 3 0 0 0-3 3v3.162A3.756 3.756 0 0 1 4.094 9h15.812ZM4.094 10.5a2.25 2.25 0 0 0-2.227 2.568l.857 6A2.25 2.25 0 0 0 4.951 21H19.05a2.25 2.25 0 0 0 2.227-1.932l.857-6a2.25 2.25 0 0 0-2.227-2.568H4.094Z" />
                    </svg>
                      Files
                   </a>
                </li>
                <% if (features.includes('players')) { %>
                <li class="transition">
                  <a href="/server/<%= server.UUID %>/players" class="nav-link2 py-2 px-3 transition border hover:bg-neutral-100 dark:hover:bg-white/5 border-transparent hover:text-neutral-900 dark:hover:text-white hover:shadow rounded-xl">
                    <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" fill="currentColor" viewBox="0 0 256 256" class="size-5 mb-0.5 inline-flex mr-1"><path d="M100,124a12,12,0,1,1,12-12A12,12,0,0,1,100,124Zm56-24a12,12,0,1,0,12,12A12,12,0,0,0,156,100Zm-4.27,45.23a45,45,0,0,1-47.46,0,8,8,0,0,0-8.54,13.54,61,61,0,0,0,64.54,0,8,8,0,0,0-8.54-13.54ZM216,80v96a32.06,32.06,0,0,1-24,31v17a16,16,0,0,1-16,16H80a16,16,0,0,1-16-16V207a32.06,32.06,0,0,1-24-31V80A32,32,0,0,1,72,48H88V32a16,16,0,0,1,16-16h48a16,16,0,0,1,16,16V48h16A32,32,0,0,1,216,80ZM104,48h48V32H104Zm72,176V208H80v16ZM200,80a16,16,0,0,0-16-16H72A16,16,0,0,0,56,80v96a16,16,0,0,0,16,16H184a16,16,0,0,0,16-16Z"></path></svg>
                     Players
                  </a>
               </li>
               <% } %>
               <% if (features.includes('worlds')) { %>
                <li class="transition">
                  <a href="/server/<%= server.UUID %>/worlds" class="nav-link2 py-2 px-3 transition border hover:bg-neutral-100 dark:hover:bg-white/5 border-transparent hover:text-neutral-900 dark:hover:text-white hover:shadow rounded-xl">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-5 mb-0.5 inline-flex mr-1">
                      <path stroke-linecap="round" stroke-linejoin="round" d="m20.893 13.393-1.135-1.135a2.252 2.252 0 0 1-.421-.585l-1.08-2.16a.414.414 0 0 0-.663-.107.827.827 0 0 1-.812.21l-1.273-.363a.89.89 0 0 0-.738 1.595l.587.39c.59.395.674 1.23.172 1.732l-.2.2c-.212.212-.33.498-.33.796v.41c0 .409-.11.809-.32 1.158l-1.315 2.191a2.11 2.11 0 0 1-1.81 1.025 1.055 1.055 0 0 1-1.055-1.055v-1.172c0-.92-.56-1.747-1.414-2.089l-.655-.261a2.25 2.25 0 0 1-1.383-2.46l.007-.042a2.25 2.25 0 0 1 .29-.787l.09-.15a2.25 2.25 0 0 1 2.37-1.048l1.178.236a1.125 1.125 0 0 0 1.302-.795l.208-.73a1.125 1.125 0 0 0-.578-1.315l-.665-.332-.091.091a2.25 2.25 0 0 1-1.591.659h-.18c-.249 0-.487.1-.662.274a.931.931 0 0 1-1.458-1.137l1.411-2.353a2.25 2.25 0 0 0 .286-.76m11.928 9.869A9 9 0 0 0 8.965 3.525m11.928 9.868A9 9 0 1 1 8.965 3.525" />
                    </svg>
                     Worlds
                  </a>
               </li>
               <% } %>
               <li class="transition">
                <a href="/server/<%= server.UUID %>/startup" class="nav-link2 py-2 px-3 transition border hover:bg-neutral-100 dark:hover:bg-white/5 border-transparent hover:text-neutral-900 dark:hover:text-white hover:shadow rounded-xl">
                 <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-5 mb-0.5 inline-flex mr-1">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M5.25 5.653c0-.856.917-1.398 1.667-.986l11.54 6.347a1.125 1.125 0 0 1 0 1.972l-11.54 6.347a1.125 1.125 0 0 1-1.667-.986V5.653Z" />
                </svg>

                   Startup
                </a>
             </li>
             <li class="transition">
                <a href="/server/<%= server.UUID %>/settings" class="nav-link2 py-2 px-3 transition border hover:bg-neutral-100 dark:hover:bg-white/5 border-transparent hover:text-neutral-900 dark:hover:text-white hover:shadow rounded-xl">
                 <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="size-5 mb-0.5 inline-flex mr-1">
                   <path fill-rule="evenodd" d="M11.078 2.25c-.917 0-1.699.663-1.85 1.567L9.05 4.889c-.02.12-.115.26-.297.348a7.493 7.493 0 0 0-.986.57c-.166.115-.334.126-.45.083L6.3 5.508a1.875 1.875 0 0 0-2.282.819l-.922 1.597a1.875 1.875 0 0 0 .432 2.385l.84.692c.095.078.17.229.154.43a7.598 7.598 0 0 0 0 1.139c.015.2-.059.352-.153.43l-.841.692a1.875 1.875 0 0 0-.432 2.385l.922 1.597a1.875 1.875 0 0 0 2.282.818l1.019-.382c.115-.043.283-.031.45.082.312.214.641.405.985.57.182.088.277.228.297.35l.178 1.071c.151.904.933 1.567 1.85 1.567h1.844c.916 0 1.699-.663 1.85-1.567l.178-1.072c.02-.12.114-.26.297-.349.344-.165.673-.356.985-.57.167-.114.335-.125.45-.082l1.02.382a1.875 1.875 0 0 0 2.28-.819l.923-1.597a1.875 1.875 0 0 0-.432-2.385l-.84-.692c-.095-.078-.17-.229-.154-.43a7.614 7.614 0 0 0 0-1.139c-.016-.2.059-.352.153-.43l.84-.692c.708-.582.891-1.59.433-2.385l-.922-1.597a1.875 1.875 0 0 0-2.282-.818l-1.02.382c-.114.043-.282.031-.449-.083a7.49 7.49 0 0 0-.985-.57c-.183-.087-.277-.227-.297-.348l-.179-1.072a1.875 1.875 0 0 0-1.85-1.567h-1.843ZM12 15.75a3.75 3.75 0 1 0 0-7.5 3.75 3.75 0 0 0 0 7.5Z" clip-rule="evenodd" />
                 </svg>
                   Settings
                </a>
             </li>
             <% } else {
                menuItems.forEach(item => {
                  if (item.feature && !features.includes(item.feature)) return;
                  const url = item.url.replace(':uuid', server.UUID);
             %>
                <li class="transition">
                  <a href="<%= url %>" class="nav-link2 py-2 px-3 transition border hover:bg-neutral-100 dark:hover:bg-white/5 border-transparent hover:text-neutral-900 dark:hover:text-white hover:shadow rounded-xl">
                    <%- item.icon %>
                    <%= item.label %>
                  </a>
                </li>
             <% }); } %>
             </ul>
          </nav>
       </div>
    </div>
 </div>

 <!-- Server Sections -->
 <% if (global.uiComponentStore) {
    const sections = global.uiComponentStore.getServerSections();

    if (sections && sections.length > 0) {
 %>
 <div class="px-8 mt-8">
    <% sections.forEach(section => { %>
      <div class="bg-white dark:bg-white/5 rounded-xl p-6 shadow-lg border border-neutral-300 dark:border-neutral-800/20 mb-6">
        <h2 class="text-lg font-semibold mb-4 text-neutral-800 dark:text-white"><%= section.title %></h2>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <%
            const items = section.items || [];

            items.forEach(item => {
              let value = item.value;

              if (value.includes(':uuid')) value = value.replace(':uuid', server.UUID);
              if (value.includes(':address')) {
                const primaryPort = server.Ports ? JSON.parse(server.Ports).find(p => p.primary) : null;
                const portNumber = primaryPort ? primaryPort.Port.split(':')[1] : '';
                value = value.replace(':address', `${server.node.address}:${portNumber}`);
              }
              if (value.includes(':image')) value = value.replace(':image', server.image.name || 'Unknown');
              if (value.includes(':node')) value = value.replace(':node', server.node.name);
              if (value.includes(':memory')) value = value.replace(':memory', `${server.Memory} MB`);
              if (value.includes(':cpu')) value = value.replace(':cpu', `${server.Cpu}%`);
              if (value.includes(':disk')) value = value.replace(':disk', `${server.Storage} MB`);
          %>
            <div>
              <h3 class="text-sm font-medium text-neutral-300 mb-2"><%= item.label %></h3>
              <% if (item.type === 'link') { %>
                <a href="<%= item.url %>" class="text-neutral-400 hover:text-neutral-300 transition"><%= value %></a>
              <% } else if (item.type === 'button') { %>
                <button onclick="<%= item.onClick %>" class="text-neutral-400 hover:text-neutral-300 transition"><%= value %></button>
              <% } else { %>
                <p class="text-neutral-400"><%= value %></p>
              <% } %>
            </div>
          <% }); %>
        </div>
      </div>
    <% }); %>
 </div>
 <% } } %>

 <script>
   document.addEventListener("DOMContentLoaded", function() {
     var currentPagePath = window.location.pathname;
     var navLinks2 = document.querySelectorAll('.nav-link2');
     var slidingBackground = document.getElementById('sliding-background');

     function updateActiveLink(clickedLink = null) {
      var activeLink = null;
      navLinks2.forEach(function(link) {
         if (currentPagePath.startsWith(link.getAttribute('href'))) {
            activeLink = link;
         }
      });
       if (activeLink) {
         navLinks2.forEach(link => {
           link.classList.remove('text-neutral-900', 'dark:text-white', 'font-medium', 'bg-white/10', 'shadow-sm');
         });

         activeLink.classList.add('text-neutral-900', 'dark:text-white', 'font-medium');

         var linkRect = activeLink.getBoundingClientRect();
         var navRect = activeLink.closest('nav').getBoundingClientRect();
         slidingBackground.style.width = linkRect.width + 'px';
         slidingBackground.style.transform = `translateX(${linkRect.left - navRect.left}px)`;
       }
     }

     // Initial setup
     updateActiveLink();

     navLinks2.forEach(function(link) {
       link.addEventListener('click', function(e) {
         e.preventDefault();
         var href = this.getAttribute('href');

         updateActiveLink(this);

         setTimeout(() => {
           window.location.href = href;
         }, 300);
       });
     });

     window.addEventListener('popstate', function() {
       currentPagePath = window.location.pathname;
       updateActiveLink();
     });
   });
 </script>