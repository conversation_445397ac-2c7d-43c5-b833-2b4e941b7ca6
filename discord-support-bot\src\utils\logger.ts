import winston from 'winston';
import { config } from './config';

const logger = winston.createLogger({
  level: config.logLevel,
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  defaultMeta: { service: 'airlink-discord-bot' },
  transports: [
    new winston.transports.File({ 
      filename: config.logFile.replace('.log', '-error.log'), 
      level: 'error' 
    }),
    new winston.transports.File({ 
      filename: config.logFile 
    })
  ]
});

// Add console logging in development
if (process.env['NODE_ENV'] !== 'production') {
  logger.add(new winston.transports.Console({
    format: winston.format.combine(
      winston.format.colorize(),
      winston.format.simple()
    )
  }));
}

export default logger;
