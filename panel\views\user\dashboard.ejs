<%- include('../components/header', { title: 'Servers' }) %>

<main class="h-screen m-auto">
  <div class="flex h-screen">
    <!-- Sidebar -->
    <div class="w-60 h-full">
      <%- include('../components/template') %>
    </div>

    <!-- Content -->
    <div class="flex-1 p-6 overflow-y-auto pt-16">
      <!-- <PERSON> Header -->
      <div class="sm:flex sm:items-center px-8 pt-4">
        <div class="sm:flex-auto">
          <h1 class="text-base font-medium leading-6 text-neutral-800 dark:text-white"><%= req.translations.userDashboardTitle %></h1>
          <p class="mt-1 tracking-tight text-sm text-neutral-500"><%= req.translations.userDashboardText %></p>
        </div>
        <% if (servers && servers.length > 0) { %>
        <div class="mt-4 sm:mt-0">
          <div class="flex items-center space-x-2 bg-gray-100 dark:bg-neutral-800 p-1 rounded-lg">
            <button id="gridViewBtn" class="view-toggle-btn px-3 py-1.5 text-sm font-medium rounded-md flex items-center justify-center active">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
              </svg>
              Grid
            </button>
            <button id="listViewBtn" class="view-toggle-btn px-3 py-1.5 text-sm font-medium rounded-md flex items-center justify-center">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
              </svg>
              List
            </button>
          </div>
        </div>
        <% } %>
      </div>

      <!-- Error Notifications -->
      <% if (req.query.err === "NOTACTIVEYET") { %>
        <div class="rounded-xl bg-amber-100 dark:bg-amber-800/10 px-4 py-6 m-7">
          <div class="flex">
            <div class="flex-shrink-0 ml-1.5">
              <svg class="animate-spin h-5 w-5 text-amber-500 dark:text-amber-400 mt-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
            </div>
            <div class="ml-5">
              <h3 class="text-sm font-medium text-amber-700 dark:text-amber-400">Awaiting Installation</h3>
              <p class="text-sm text-amber-500 dark:text-amber-400/50">The server either isn't installed yet, or is in a failed state.</p>
            </div>
          </div>
        </div>
      <% } %>

      <% if (typeof daemonOffline !== 'undefined' && daemonOffline) { %>
        <div class="rounded-xl bg-red-100 dark:bg-red-800/10 px-4 py-6 m-7">
          <div class="flex">
            <div class="flex-shrink-0 ml-1.5">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-red-500 dark:text-red-400 mt-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
              </svg>
            </div>
            <div class="ml-5">
              <h3 class="text-sm font-medium text-red-700 dark:text-red-400">Connection Error</h3>
              <p class="text-sm text-red-500 dark:text-red-400/50">One or more nodes are offline. Some server information may be unavailable.</p>
              <button onclick="window.location.reload()" class="mt-2 px-3 py-1 bg-red-600 hover:bg-red-700 text-white text-xs rounded-lg transition-colors">
                Retry Connection
              </button>
            </div>
          </div>
        </div>
      <% } %>

      <!-- Servers Section -->
      <div class="px-4 sm:px-6 lg:px-8 mt-8">
        <% if (servers.length === 0) { %>
          <!-- No Servers Message -->
          <div id="noServersContainer" class="text-center mt-64">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"
            class="mx-auto h-28 w-28 text-neutral-400 dark:text-neutral-500">
            <path fill-rule="evenodd"
              d="M11.622 1.602a.75.75 0 0 1 .756 0l2.25 1.313a.75.75 0 0 1-.756 1.295L12 3.118 10.128 4.21a.75.75 0 1 1-.756-1.295l2.25-1.313ZM5.898 5.81a.75.75 0 0 1-.27 1.025l-1.14.665 1.14.665a.75.75 0 1 1-.756 1.295L3.75 8.806v.944a.75.75 0 0 1-1.5 0V7.5a.75.75 0 0 1 .372-.648l2.25-1.312a.75.75 0 0 1 1.026.27Zm12.204 0a.75.75 0 0 1 1.026-.27l2.25 1.312a.75.75 0 0 1 .372.648v2.25a.75.75 0 0 1-1.5 0v-.944l-1.122.654a.75.75 0 1 1-.756-1.295l1.14-.665-1.14-.665a.75.75 0 0 1-.27-1.025Zm-9 5.25a.75.75 0 0 1 1.026-.27L12 11.882l1.872-1.092a.75.75 0 1 1 .756 1.295l-1.878 1.096V15a.75.75 0 0 1-1.5 0v-1.82l-1.878-1.095a.75.75 0 0 1-.27-1.025ZM3 13.5a.75.75 0 0 1 .75.75v1.82l1.878 1.095a.75.75 0 1 1-.756 1.295l-2.25-1.312a.75.75 0 0 1-.372-.648v-2.25A.75.75 0 0 1 3 13.5Zm18 0a.75.75 0 0 1 .75.75v2.25a.75.75 0 0 1-.372.648l-2.25 1.312a.75.75 0 1 1-.756-1.295l1.878-1.096V14.25a.75.75 0 0 1 .75-.75Zm-9 5.25a.75.75 0 0 1 .75.75v.944l1.122-.654a.75.75 0 1 1 .756 1.295l-2.25 1.313a.75.75 0 0 1-.756 0l-2.25-1.313a.75.75 0 1 1 .756-1.295l1.122.654V19.5a.75.75 0 0 1 .75-.75Z"
              clip-rule="evenodd" />
          </svg>
            <h2 class="mt-8 text-lg font-medium text-gray-900 dark:text-white">
              Oops! We couldn't find any servers associated with your account.
            </h3>
            <p class="mt-1 text-sm text-gray-400 dark:text-neutral-400">
              You don’t have any servers yet.
              <% if (user.isAdmin) { %>
                Why not create one now? <a href="/admin/servers/create" class="text-blue-500 hover:underline">Create a server</a>
              <% } else { %>
                Just wait for an admin to assign one to you!
              <% } %>
            </p>
          </div>
        <% } else { %>
          <!-- Grid View -->
          <div id="gridView" class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
            <% servers.forEach(server => { %>
              <div onclick="window.location.href = '../server/<%= server.UUID %>';"
                  class="bg-white dark:bg-neutral-800/50 shadow rounded-xl overflow-hidden border border-gray-200 dark:border-neutral-700/30 hover:shadow-lg dark:hover:bg-neutral-700/50 transition-all duration-200 cursor-pointer">
                <div class="p-5">
                  <div class="flex justify-between items-start">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white truncate"><%= server.name %></h3>
                    <span class="inline-flex items-center px-2.5 py-1 text-xs font-medium rounded-md shadow-sm
                        <%= server.status === 'running' ? 'bg-emerald-50 dark:bg-emerald-500/20 text-emerald-600 dark:text-emerald-400 border border-emerald-200 dark:border-emerald-500/30' : 'bg-rose-50 dark:bg-rose-500/10 text-rose-600 dark:text-rose-400 border border-rose-200 dark:border-rose-500/20' %>">
                      <span class="relative flex h-2 w-2 mr-1.5">
                        <% if (server.status === 'running') { %>
                          <span class="animate-ping absolute inline-flex h-full w-full rounded-full bg-emerald-400 opacity-75"></span>
                          <span class="relative inline-flex rounded-full h-2 w-2 bg-emerald-500"></span>
                        <% } else { %>
                          <span class="relative inline-flex rounded-full h-2 w-2 bg-rose-500"></span>
                        <% } %>
                      </span>
                      <%= server.status === 'running' ? 'Running' : 'Stopped' %>
                    </span>
                  </div>

                  <div class="mt-4 space-y-3">
                    <div class="flex items-center justify-between">
                      <span class="text-sm text-gray-500 dark:text-neutral-400">RAM Usage</span>
                      <span class="text-sm font-medium text-gray-700 dark:text-neutral-300 border border-gray-200 dark:border-neutral-700/30 bg-gray-50 dark:bg-neutral-800/50 px-2.5 py-1 rounded-md shadow-sm"><%= server.ramUsage %>%</span>
                    </div>
                    <div class="flex items-center justify-between">
                      <span class="text-sm text-gray-500 dark:text-neutral-400">CPU Usage</span>
                      <span class="text-sm font-medium text-gray-700 dark:text-neutral-300 border border-gray-200 dark:border-neutral-700/30 bg-gray-50 dark:bg-neutral-800/50 px-2.5 py-1 rounded-md shadow-sm"><%= server.cpuUsage %>%</span>
                    </div>
                  </div>
                </div>
                <div class="bg-gray-50 dark:bg-neutral-700/30 px-5 py-3">
                  <div class="text-sm text-gray-500 dark:text-neutral-400 flex justify-between items-center">
                    <span class="inline-block">
                      <% if (server.status === 'running') { %>
                        <span class="flex items-center">
                          <span class="relative flex h-2 w-2 mr-2">
                            <span class="animate-ping absolute inline-flex h-full w-full rounded-full bg-emerald-400 opacity-75"></span>
                            <span class="relative inline-flex rounded-full h-2 w-2 bg-emerald-500"></span>
                          </span>
                          Online
                        </span>
                      <% } else { %>
                        <span class="flex items-center">
                          <span class="relative flex h-2 w-2 mr-2">
                            <span class="relative inline-flex rounded-full h-2 w-2 bg-rose-500"></span>
                          </span>
                          Offline
                        </span>
                      <% } %>
                    </span>
                    <a href="../server/<%= server.UUID %>" class="text-blue-500 hover:text-blue-600 dark:text-blue-400 dark:hover:text-blue-300 font-medium">
                      Manage
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 inline-block ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
                      </svg>
                    </a>
                  </div>
                </div>
              </div>
            <% }); %>
          </div>

          <!-- List View -->
          <div id="listView" class="overflow-hidden shadow ring-1 ring-gray-200 dark:ring-white/5 rounded-lg bg-white dark:bg-transparent hidden">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-white/10">
              <thead class="bg-gray-50 dark:bg-white/5">
                <tr>
                  <th class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 dark:text-white sm:pl-6">Server</th>
                  <th class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-white">Status</th>
                  <th class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-white">RAM Usage</th>
                  <th class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-white">CPU Usage</th>
                </tr>
              </thead>
              <tbody class="divide-y divide-gray-200 dark:divide-white/10 bg-gray-50 dark:bg-white/5">
                <% servers.forEach(server => { %>
                  <tr onclick="window.location.href = '../server/<%= server.UUID %>';" class="hover:bg-gray-100 dark:hover:bg-white/10 cursor-pointer">
                    <td class="py-4 pl-4 pr-3 text-sm font-medium text-gray-900 dark:text-white sm:pl-6"><%= server.name %></td>
                    <td class="px-3 py-4 text-sm">
                      <span class="inline-flex items-center px-2.5 py-1 text-xs font-medium rounded-md shadow-sm
                          <%= server.status === 'running' ? 'bg-emerald-50 dark:bg-emerald-500/20 text-emerald-600 dark:text-emerald-400 border border-emerald-200 dark:border-emerald-500/30' : 'bg-rose-50 dark:bg-rose-500/10 text-rose-600 dark:text-rose-400 border border-rose-200 dark:border-rose-500/20' %>">
                        <span class="relative flex h-2 w-2 mr-1.5">
                          <% if (server.status === 'running') { %>
                            <span class="animate-ping absolute inline-flex h-full w-full rounded-full bg-emerald-400 opacity-75"></span>
                            <span class="relative inline-flex rounded-full h-2 w-2 bg-emerald-500"></span>
                          <% } else { %>
                            <span class="relative inline-flex rounded-full h-2 w-2 bg-rose-500"></span>
                          <% } %>
                        </span>
                        <%= server.status === 'running' ? 'Running' : 'Stopped' %>
                      </span>
                    </td>
                    <td class="px-3 py-4 text-sm text-gray-700 dark:text-neutral-300"><span class="border border-gray-200 dark:border-neutral-700/30 bg-gray-50 dark:bg-neutral-800/50 px-2.5 py-1 rounded-md shadow-sm"><%= server.ramUsage %>%</span></td>
                    <td class="px-3 py-4 text-sm text-gray-700 dark:text-neutral-300"><span class="border border-gray-200 dark:border-neutral-700/30 bg-gray-50 dark:bg-neutral-800/50 px-2.5 py-1 rounded-md shadow-sm"><%= server.cpuUsage %>%</span></td>
                  </tr>
                <% }); %>
              </tbody>
            </table>
          </div>


          <!-- Paginator -->
          <div class="flex justify-center py-4">
            <% if (currentPage > 1) { %>
              <a href="/?page=<%= currentPage - 1 %>" class="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-lg shadow-sm transition duration-300 ease-in-out mr-2">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" stroke="currentColor" viewBox="0 0 24 24" class="h-5 w-5 text-white mr-2">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                </svg>
                <%= currentPage - 1 %>
              </a>
            <% } %>
            <div class="inline-flex items-center text-sm font-medium text-gray-700 dark:text-neutral-300">
              Page <span class="font-semibold text-blue-600 dark:text-blue-400 ml-1 mr-1"><%= currentPage %></span> of <span class="font-semibold text-blue-600 dark:text-blue-400 ml-1"><%= totalPages %></span>
            </div>
            <% if (currentPage < totalPages) { %>
              <a href="/?page=<%= currentPage + 1 %>" class="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-lg shadow-sm transition duration-300 ease-in-out ml-2">
                <%= currentPage + 1 %>
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" stroke="currentColor" viewBox="0 0 24 24" class="h-5 w-5 text-white ml-2">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
              </a>
            <% } %>
          </div>

        <% } %>
      </div>
    </div>
  </div>
</main>

<%- include('../components/footer') %>

<style>
  .view-toggle-btn.active {
    background-color: rgba(59, 130, 246, 0.1);
    color: #3b82f6;
  }

  .dark .view-toggle-btn.active {
    background-color: rgba(59, 130, 246, 0.2);
    color: #60a5fa;
  }
</style>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Get view elements
    const gridView = document.getElementById('gridView');
    const listView = document.getElementById('listView');
    const gridViewBtn = document.getElementById('gridViewBtn');
    const listViewBtn = document.getElementById('listViewBtn');

    // Check if elements exist (they might not if there are no servers)
    if (!gridView || !listView || !gridViewBtn || !listViewBtn) return;

    // Get saved view preference from localStorage
    const savedView = localStorage.getItem('serverViewPreference') || 'grid';

    // Set initial view based on saved preference
    if (savedView === 'list') {
      gridView.classList.add('hidden');
      listView.classList.remove('hidden');
      gridViewBtn.classList.remove('active');
      listViewBtn.classList.add('active');
    }

    // Add click event listeners to buttons
    gridViewBtn.addEventListener('click', function() {
      gridView.classList.remove('hidden');
      listView.classList.add('hidden');
      gridViewBtn.classList.add('active');
      listViewBtn.classList.remove('active');
      localStorage.setItem('serverViewPreference', 'grid');
    });

    listViewBtn.addEventListener('click', function() {
      gridView.classList.add('hidden');
      listView.classList.remove('hidden');
      gridViewBtn.classList.remove('active');
      listViewBtn.classList.add('active');
      localStorage.setItem('serverViewPreference', 'list');
    });
  });
</script>
