# 🤖 Airlink Discord Support Bot - Complete Setup Guide

## 📋 What You'll Need

1. **Discord Bot Token** - Get this from Discord Developer Portal
2. **Discord Client ID** - Also from Discord Developer Portal  
3. **Node.js 18+** - Already configured with your custom AI API

## 🚀 Quick Setup (5 minutes)

### Option 1: Super Easy Windows Setup
1. Double-click `start.bat` in the bot folder
2. Follow the prompts to enter your Discord credentials
3. The bot will automatically install, build, and start!

### Option 2: Interactive Setup
```bash
node setup.js
npm install
npm run build
npm start
```

## 🔧 Discord Bot Creation

### Step 1: Create the Bot
1. Go to https://discord.com/developers/applications
2. Click "New Application" → Name it "Airlink Support Bot"
3. Go to "Bot" section → Click "Add Bot"
4. **Copy the Token** (keep this secret!)

### Step 2: Set Permissions
Enable these permissions in the Bot section:
- ✅ Send Messages
- ✅ Send Messages in Threads  
- ✅ Embed Links
- ✅ Attach Files
- ✅ Read Message History
- ✅ Use External Emojis
- ✅ Add Reactions
- ✅ Read Messages/View Channels

### Step 3: Get Client ID
1. Go to "General Information"
2. **Copy the Application ID** (this is your Client ID)

### Step 4: Invite Bot to Server
1. Go to "OAuth2" → "URL Generator"
2. Select "bot" scope
3. Select all the permissions from Step 2
4. Copy the URL and open it to invite the bot

### Step 5: Get Support Channel ID (Optional)
If you want the bot to respond to ALL messages in a specific channel:
1. Enable Developer Mode in Discord (User Settings → Advanced → Developer Mode)
2. Right-click your support channel
3. Click "Copy Channel ID"
4. Save this ID - you'll need it during setup

## 🎯 Bot Features

### 💬 Conversation Support
- Responds to direct messages
- Responds when mentioned (@BotName)
- Responds to messages starting with ! (configurable)
- **Responds to ALL messages in a dedicated support channel** (if configured)
- Responds in channels with "support" in the name
- Maintains conversation context for better help

### 🖼️ Image Analysis
- Upload error screenshots
- Bot extracts text from images
- Provides specific troubleshooting for Airlink issues
- Supports: PNG, JPG, JPEG, GIF, WebP (up to 10MB)

### 🧠 Smart Error Detection
Automatically detects and provides solutions for:
- Docker connection issues
- Database connection problems
- Node.js/npm installation errors
- Port conflicts
- Permission issues
- Configuration problems

### 📚 Airlink Knowledge Base
Comprehensive help for:
- Panel installation and setup
- Daemon configuration
- Database setup
- Addon development
- Common troubleshooting

## 🔧 Configuration

The bot is pre-configured with your AI API:
- **API URL**: `https://api.electronhub.ai/v1/chat/completions`
- **Model**: `gpt-4o-mini`
- **API Key**: Already set (secure)

You only need to provide:
- Discord Bot Token
- Discord Client ID

## 📝 Usage Examples

### Text Support
```
User: "How do I install Airlink Panel?"
Bot: [Provides step-by-step installation guide]

User: "I'm getting a Docker error"
Bot: [Analyzes error and provides specific solutions]
```

### Image Support
```
User: [Uploads error screenshot]
Bot: 🔍 Image Analysis Results
     📝 Extracted Text: "Cannot connect to Docker daemon"
     ⚠️ Error Type: Docker Connection
     💡 Suggestions: 1. Start Docker service...
```

## 🛠️ Commands

- `!help` - Show help information
- `!status` - Bot status and stats
- `!clear` - Clear conversation history
- Direct messages and mentions work automatically

## 📊 Monitoring

- Logs saved to `logs/bot.log`
- Error logs in `logs/bot-error.log`
- Console output in development mode

## 🔒 Security

- API keys are stored securely in .env file
- Never commit .env to version control
- Bot only responds in appropriate channels
- Image processing has size and format limits

## 🆘 Troubleshooting

### Bot Won't Start
1. Check Discord token is correct
2. Ensure Node.js 18+ is installed
3. Run `npm install` to install dependencies
4. Check logs in `logs/` folder

### Bot Not Responding
1. Verify bot has proper permissions
2. Check if bot is online in Discord
3. Try mentioning the bot directly
4. Check server logs for errors

### Image Analysis Not Working
1. Ensure image is under 10MB
2. Use supported formats (PNG, JPG, etc.)
3. Check internet connection for AI API
4. Verify AI API key is correct

## 📞 Support

If you need help with the bot:
1. Check the logs in `logs/bot.log`
2. Verify your Discord bot setup
3. Test with simple text messages first
4. Check the Airlink Discord for community support

## 🎉 You're All Set!

Your Airlink Discord Support Bot is ready to help your community with:
- ✅ 24/7 AI-powered support
- ✅ Image error analysis
- ✅ Comprehensive Airlink knowledge
- ✅ Smart error detection
- ✅ Conversation memory

Happy supporting! 🚀
