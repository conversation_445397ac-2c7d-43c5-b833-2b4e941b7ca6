import fs from 'fs';
import path from 'path';
import logger from '../utils/logger';

interface KnowledgeEntry {
  problem: string;
  keywords: string[];
  solution: string;
}

export class SimpleKnowledgeBase {
  private entries: KnowledgeEntry[] = [];
  private knowledgeBasePath: string;

  constructor() {
    this.knowledgeBasePath = path.join(process.cwd(), 'knowledgebase.txt');
    this.loadKnowledgeBase();
  }

  private loadKnowledgeBase(): void {
    try {
      if (!fs.existsSync(this.knowledgeBasePath)) {
        logger.warn('Knowledge base file not found:', this.knowledgeBasePath);
        return;
      }

      const content = fs.readFileSync(this.knowledgeBasePath, 'utf-8');
      this.parseKnowledgeBase(content);
      logger.info(`Loaded ${this.entries.length} knowledge base entries`);
    } catch (error) {
      logger.error('Failed to load knowledge base:', error);
    }
  }

  private parseKnowledgeBase(content: string): void {
    const lines = content.split('\n');
    let currentEntry: Partial<KnowledgeEntry> = {};

    for (const line of lines) {
      const trimmedLine = line.trim();
      
      if (trimmedLine.startsWith('PROBLEM:')) {
        // Save previous entry if complete
        if (currentEntry.problem && currentEntry.keywords && currentEntry.solution) {
          this.entries.push(currentEntry as KnowledgeEntry);
        }
        
        // Start new entry
        currentEntry = {
          problem: trimmedLine.substring(8).trim()
        };
      } else if (trimmedLine.startsWith('KEYWORDS:')) {
        const keywordString = trimmedLine.substring(9).trim();
        currentEntry.keywords = keywordString.split(',').map(k => k.trim().toLowerCase());
      } else if (trimmedLine.startsWith('SOLUTION:')) {
        currentEntry.solution = trimmedLine.substring(9).trim();
      }
    }

    // Add the last entry
    if (currentEntry.problem && currentEntry.keywords && currentEntry.solution) {
      this.entries.push(currentEntry as KnowledgeEntry);
    }
  }

  searchProblem(query: string): KnowledgeEntry | null {
    const queryLower = query.toLowerCase();
    const queryWords = queryLower.split(/\s+/).filter(word => word.length > 2);

    let bestMatch: KnowledgeEntry | null = null;
    let bestScore = 0;

    for (const entry of this.entries) {
      let score = 0;

      // Check for exact keyword matches
      for (const keyword of entry.keywords) {
        if (queryLower.includes(keyword)) {
          score += 3; // High score for keyword match
        }
      }

      // Check for word matches in keywords
      for (const queryWord of queryWords) {
        for (const keyword of entry.keywords) {
          if (keyword.includes(queryWord) || queryWord.includes(keyword)) {
            score += 1;
          }
        }
      }

      // Check for matches in problem description
      for (const queryWord of queryWords) {
        if (entry.problem.toLowerCase().includes(queryWord)) {
          score += 0.5;
        }
      }

      if (score > bestScore && score >= 2) { // Minimum threshold
        bestScore = score;
        bestMatch = entry;
      }
    }

    return bestMatch;
  }

  getAllProblems(): string[] {
    return this.entries.map(entry => entry.problem);
  }

  getEntriesCount(): number {
    return this.entries.length;
  }

  reloadKnowledgeBase(): void {
    this.entries = [];
    this.loadKnowledgeBase();
  }

  addEntry(problem: string, keywords: string[], solution: string): void {
    this.entries.push({ problem, keywords, solution });
    this.saveKnowledgeBase();
  }

  private saveKnowledgeBase(): void {
    try {
      let content = 'AIRLINK PANEL KNOWLEDGE BASE\n\n';
      
      for (const entry of this.entries) {
        content += `PROBLEM: ${entry.problem}\n`;
        content += `KEYWORDS: ${entry.keywords.join(', ')}\n`;
        content += `SOLUTION: ${entry.solution}\n\n`;
      }

      fs.writeFileSync(this.knowledgeBasePath, content, 'utf-8');
      logger.info('Knowledge base saved successfully');
    } catch (error) {
      logger.error('Failed to save knowledge base:', error);
    }
  }
}
