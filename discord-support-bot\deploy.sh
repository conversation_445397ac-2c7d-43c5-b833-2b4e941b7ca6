#!/bin/bash

echo "🚀 Airlink Discord Support Bot - Deployment Script"
echo "=================================================="

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js v18+ first."
    exit 1
fi

# Check Node.js version
NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    echo "❌ Node.js version $NODE_VERSION is too old. Please install Node.js v18+."
    exit 1
fi

echo "✅ Node.js $(node -v) detected"

# Install dependencies
echo "📦 Installing dependencies..."
npm install

if [ $? -ne 0 ]; then
    echo "❌ Failed to install dependencies"
    exit 1
fi

# Build the bot
echo "🔨 Building bot..."
npm run build

if [ $? -ne 0 ]; then
    echo "❌ Failed to build bot"
    exit 1
fi

# Create logs directory
mkdir -p logs

# Check if .env exists
if [ ! -f ".env" ]; then
    echo "⚠️  .env file not found. Running setup..."
    node setup.js
fi

echo ""
echo "✅ Deployment complete!"
echo ""
echo "🎯 Next steps:"
echo "1. Start the bot: npm start"
echo "2. Or use PM2: pm2 start dist/index.js --name 'airlink-discord-bot'"
echo "3. Check logs: tail -f logs/bot.log"
echo ""
echo "📚 Admin commands:"
echo "- !kb status - Show knowledge base stats"
echo "- !kb search <query> - Search knowledge base"
echo "- !kb reload - Reload knowledge base"
echo ""
echo "🔧 Bot responds to:"
echo "- Direct messages"
echo "- @mentions"
echo "- @mentions in dedicated support channel"
echo "- Messages starting with !"
echo ""
