<%- include('../components/header', { title: 'Register'}) %>
<div class="min-h-full flex">
  <div class="flex-1 flex flex-col justify-center py-12 px-4 sm:px-6 lg:flex-none lg:px-20 xl:px-24">
    <div class="mx-auto w-full max-w-sm lg:w-96">
      <div>
        <img class="h-12 w-auto rounded-xl" src="<%= settings.logo %>" alt="">
        <h2 class="mt-6 text-3xl font-medium text-white">Register an account</h2>
        <p class="mt-2 text-sm text-neutral-500">
          Please enter your details below.
        </p>
      </div>

      <div class="mt-8">
        <div class="mt-6">
          <form method="POST" action="/register" class="space-y-6">
            <%- include('../components/csrf') %>
            <div>
              <label for="username" class="block text-sm font-medium text-neutral-400"> Username </label>
              <div class="mt-1">
                <input placeholder="example" id="username" name="username" autocomplete="username" required class="appearance-none block w-full px-3 py-2 bg-white/10 border border-transparent rounded-xl placeholder-neutral-500 focus:outline-none focus:ring-neutral-500 focus:border-neutral-500 text-white transition sm:text-sm">
              </div>
            </div>

            <div>
              <label for="email" class="block text-sm font-medium text-neutral-400"> Email </label>
              <div class="mt-1">
                <input placeholder="<EMAIL>" id="email" name="email" autocomplete="email" required class="appearance-none block w-full px-3 py-2 bg-white/10 border border-transparent rounded-xl placeholder-neutral-500 focus:outline-none focus:ring-neutral-500 focus:border-neutral-500 text-white transition sm:text-sm">
              </div>
            </div>

            <div class="space-y-1">
              <label for="password" class="block text-sm font-medium text-neutral-400">Password</label>
              <div class="mt-1">
                <input
                  placeholder="********"
                  id="password"
                  name="password"
                  type="password"
                  autocomplete="new-password"
                  required
                  class="appearance-none block w-full px-3 py-2 bg-white/10 border border-transparent rounded-xl placeholder-neutral-500 focus:outline-none focus:ring-neutral-500 focus:border-neutral-500 text-white transition sm:text-sm"
                >
              </div>
            </div>

            <div>
              <button type="submit" class="w-full flex transition justify-center py-2 px-4 border border-transparent rounded-xl shadow-sm text-sm font-medium text-neutral-800 bg-white hover:bg-neutral-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-neutral-500">Register</button>
            </div>

            <% if (req.query.err && req.query.err === "weak_password") { %>
              <div class="mt-2 text-sm text-red-500">
                Password does not meet the required security criteria.
              </div>
            <% } %>
          </form>
        </div>
      </div>
      <div class="mt-6 text-sm text-neutral-500">
        Already have an account? <a href="/login" class="font-normal text-neutral-500 hover:text-white transition ">Sign in</a>
      </div>
    </div>
  </div>
  <div class="hidden lg:block relative w-0 flex-1">
    <img class="absolute inset-0 h-full w-full object-cover" src="https://i.imgur.com/8G5eRWX.jpeg" alt="">
  </div>
</div>
<%- include('../components/footer') %>

<script>
  const passwordInput = document.getElementById('password');
  const criteriaList = document.createElement('div');
  criteriaList.className = 'mt-2 text-sm';

  const updatePasswordStrength = () => {
    const password = passwordInput.value;
    const criteria = {
      length: password.length >= 8,
      letter: /[A-Za-z]/.test(password),
      number: /\d/.test(password)
    };

    // Password validation logging
    console.group('Password Validation Check');
    console.log('Current password length:', password.length);
    console.log('Requirements status:');
    console.log('✓ Length (8+ chars):', criteria.length ? 'Passed' : 'Failed');
    console.log('✓ Contains letter:', criteria.letter ? 'Passed' : 'Failed');
    console.log('✓ Contains number:', criteria.number ? 'Passed' : 'Failed');

    // Log missing requirements
    const missing = [];
    if (!criteria.length) missing.push('Need at least 8 characters');
    if (!criteria.letter) missing.push('Need at least one letter');
    if (!criteria.number) missing.push('Need at least one number');

    if (missing.length > 0) {
      console.warn('Missing requirements:', missing);
    } else {
      console.log('✅ All requirements met!');
    }
    console.groupEnd();

    criteriaList.innerHTML = `
      <ul class="space-y-1">
        <li class="${criteria.length ? 'text-green-500' : 'text-red-500'}">
          ${criteria.length ? '✓' : '✗'} At least 8 characters
        </li>
        <li class="${criteria.letter ? 'text-green-500' : 'text-red-500'}">
          ${criteria.letter ? '✓' : '✗'} At least one letter
        </li>
        <li class="${criteria.number ? 'text-green-500' : 'text-red-500'}">
          ${criteria.number ? '✓' : '✗'} At least one number
        </li>
      </ul>
    `;
  };

  // Add debounce to avoid too frequent console logs
  let timeout;
  passwordInput.addEventListener('input', () => {
    clearTimeout(timeout);
    timeout = setTimeout(updatePasswordStrength, 500);
  });

  passwordInput.after(criteriaList);
  updatePasswordStrength();
</script>