# 🚀 Discord Support Bot - Deployment Guide

## 📦 What's Included

### Essential Files:
- `package.json` - Dependencies and scripts
- `tsconfig.json` - TypeScript configuration
- `.env` - Your configured environment (with your tokens)
- `knowledgebase.txt` - Problem/solution database
- `src/` - All source code
- `setup.js` - Setup script for new deployments
- `start.bat` - Windows startup script

### Documentation:
- `README.md` - Complete documentation
- `SETUP_GUIDE.md` - Step-by-step setup guide

## 🖥️ Server Deployment Steps

### 1. Upload Files
Upload all files to your server directory (e.g., `/var/www/discord-bot/`)

### 2. Install Dependencies
```bash
npm install
```

### 3. Build the Bot
```bash
npm run build
```

### 4. Configure Environment
Your `.env` file is already configured with:
- ✅ Discord Token
- ✅ Discord Client ID  
- ✅ Dedicated Support Channel ID
- ✅ AI API credentials

### 5. Start the Bot
```bash
# Production
npm start

# Development (with auto-restart)
npm run dev
```

## 🔧 Server Management

### Using PM2 (Recommended)
```bash
# Install PM2
npm install -g pm2

# Start bot with PM2
pm2 start dist/index.js --name "airlink-discord-bot"

# Save PM2 configuration
pm2 save
pm2 startup
```

### Manual Start
```bash
# Build and start
npm run build && npm start
```

## 📝 Knowledge Base Management

### Edit Knowledge Base
Edit `knowledgebase.txt` to add/modify problems and solutions:

```
PROBLEM: Your problem description
KEYWORDS: keyword1, keyword2, keyword3
SOLUTION: Your solution description

PROBLEM: Another problem
KEYWORDS: more, keywords, here
SOLUTION: Another solution
```

### Reload Knowledge Base
Use admin command: `!kb reload` (or restart bot)

## 🛠️ Admin Commands

### Knowledge Base Commands (Admin Only):
- `!kb status` - Show statistics
- `!kb search <query>` - Test search
- `!kb add <problem>\n<keywords>\n<solution>` - Add entry
- `!kb reload` - Reload from file

## 🔍 Bot Behavior

### Responds To:
- ✅ Direct messages (always)
- ✅ @mentions (always)  
- ✅ @mentions in dedicated support channel
- ✅ Messages starting with `!` (configurable)
- ✅ Channels with "support" in name

### Features:
- 🤖 AI-powered responses using GPT-4o mini
- 🖼️ Image analysis for error screenshots
- 📚 Knowledge base search before AI response
- 💬 Conversation memory
- 🚫 @everyone ping protection

## 📊 Monitoring

### Logs Location:
- `logs/bot.log` - General logs
- `logs/bot-error.log` - Error logs

### Check Status:
```bash
# If using PM2
pm2 status
pm2 logs airlink-discord-bot

# Manual check
tail -f logs/bot.log
```

## 🔄 Updates

### Update Knowledge Base:
1. Edit `knowledgebase.txt`
2. Use `!kb reload` command or restart bot

### Update Bot Code:
1. Upload new files
2. Run `npm run build`
3. Restart bot (`pm2 restart airlink-discord-bot`)

## ⚠️ Important Notes

- **Keep `.env` secure** - Contains sensitive tokens
- **Backup `knowledgebase.txt`** - Contains all your solutions
- **Monitor logs** - Check for errors regularly
- **Test after deployment** - Verify bot responds correctly

## 🆘 Troubleshooting

### Bot Won't Start:
1. Check `logs/bot-error.log`
2. Verify Discord token is valid
3. Ensure all dependencies installed: `npm install`

### Bot Not Responding:
1. Check bot permissions in Discord
2. Verify channel ID is correct
3. Test with direct message first

### Knowledge Base Issues:
1. Check `knowledgebase.txt` format
2. Use `!kb reload` after changes
3. Check logs for parsing errors

## 📞 Support

- Check logs in `logs/` directory
- Test with `!kb search <query>` 
- Verify bot has proper Discord permissions
- Ensure Node.js v18+ is installed
