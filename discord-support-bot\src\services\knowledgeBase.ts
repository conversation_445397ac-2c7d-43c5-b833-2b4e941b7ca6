import { AirlinkKnowledge, ErrorPattern } from '../types';
import { SimpleKnowledgeBase } from './simpleKnowledgeBase';
import logger from '../utils/logger';

export class KnowledgeBase {
  private simpleKB: SimpleKnowledgeBase;
  private knowledge: AirlinkKnowledge[] = [
    {
      category: 'Installation',
      title: 'Basic Airlink Panel Installation',
      content: `To install Airlink Panel:

1. **Prerequisites:**
   - Node.js v16+ (v18+ recommended)
   - npm v8+
   - Git
   - Supported Database (PostgreSQL/MySQL)
   - Docker (for server management)

2. **Installation Steps:**
   \`\`\`bash
   cd /var/www/
   git clone https://github.com/AirlinkLabs/panel.git
   cd panel
   sudo chown -R www-data:www-data /var/www/panel
   sudo chmod -R 755 /var/www/panel
   npm install -g typescript
   npm install --omit=dev
   cp example.env .env
   npm run migrate:dev
   npm run build
   npm run seed
   npm run start
   \`\`\`

3. **Access:** Panel will be available on port 3001 (unless changed in .env)`,
      keywords: ['install', 'setup', 'installation', 'prerequisites', 'node', 'npm', 'git'],
      relatedTopics: ['Configuration', 'Database Setup', 'Environment Variables']
    },
    {
      category: 'Configuration',
      title: 'Environment Configuration',
      content: `Airlink Panel uses environment variables for configuration:

**Key Environment Variables:**
- \`DATABASE_URL\`: Database connection string
- \`PORT\`: Panel port (default: 3001)
- \`JWT_SECRET\`: JWT signing secret
- \`ENCRYPTION_KEY\`: Data encryption key
- \`NODE_ENV\`: Environment (development/production)

**Database Configuration:**
- PostgreSQL: \`postgresql://user:password@localhost:5432/airlink\`
- MySQL: \`mysql://user:password@localhost:3306/airlink\`

**Important:** Always use strong, unique secrets in production!`,
      keywords: ['config', 'environment', 'env', 'database', 'port', 'jwt', 'encryption'],
      relatedTopics: ['Installation', 'Database Setup', 'Security']
    },
    {
      category: 'Daemon',
      title: 'Airlink Daemon Setup',
      content: `The Airlink Daemon (airlinkd) manages Docker containers on nodes:

**Installation:**
\`\`\`bash
cd /etc
git clone https://github.com/airlinklabs/airlinkd
cd airlinkd
npm install
npm run build
\`\`\`

**Configuration:**
1. Add the node in the Panel
2. Click "Configure" to get the setup command
3. Run the configuration command on the node
4. Start the daemon: \`npm run start:dev\`

**Requirements:**
- Docker installed and running
- Node.js v18+
- Network connectivity to the Panel`,
      keywords: ['daemon', 'airlinkd', 'node', 'docker', 'container', 'configure'],
      relatedTopics: ['Docker', 'Node Management', 'Network Configuration']
    },
    {
      category: 'Troubleshooting',
      title: 'Common Installation Issues',
      content: `**Node.js Version Issues:**
- Airlink requires Node.js v16+ (v18+ recommended)
- Check version: \`node --version\`
- Update Node.js if needed

**npm Install Failures:**
- Clear npm cache: \`npm cache clean --force\`
- Delete node_modules and package-lock.json, then reinstall
- Check for permission issues (avoid using sudo with npm)

**Database Connection Problems:**
- Verify database server is running
- Check DATABASE_URL format in .env
- Test connection: \`npm run migrate:dev\`
- Ensure database exists and user has permissions

**Port Already in Use:**
- Default port 3001 might be occupied
- Change PORT in .env file
- Kill existing processes: \`pkill -f airlink\``,
      keywords: ['troubleshooting', 'installation', 'node', 'npm', 'database', 'port', 'error'],
      relatedTopics: ['Installation', 'Configuration', 'Database Setup']
    },
    {
      category: 'Docker',
      title: 'Docker Configuration for Airlink',
      content: `**Docker Requirements:**
- Docker Engine 20.10+
- Docker Compose (optional but recommended)
- Sufficient disk space for images and containers

**Common Docker Issues:**
1. **Permission Denied:**
   \`\`\`bash
   sudo usermod -aG docker $USER
   newgrp docker
   \`\`\`

2. **Docker Daemon Not Running:**
   \`\`\`bash
   sudo systemctl start docker
   sudo systemctl enable docker
   \`\`\`

3. **Network Issues:**
   - Check Docker networks: \`docker network ls\`
   - Ensure ports are not blocked by firewall

**Best Practices:**
- Regular cleanup: \`docker system prune\`
- Monitor disk usage: \`docker system df\`
- Use specific image tags, not 'latest'`,
      keywords: ['docker', 'container', 'permission', 'daemon', 'network', 'cleanup'],
      relatedTopics: ['Daemon', 'Node Management', 'System Administration']
    },
    {
      category: 'Addons',
      title: 'Airlink Addon System',
      content: `**Creating Addons:**
1. Create directory in \`panel/storage/addons/your-addon-name\`
2. Add package.json with addon metadata
3. Create entry point (usually index.ts)
4. Implement addon functionality

**Addon Structure:**
\`\`\`
your-addon/
├── package.json
├── index.ts
├── views/
├── migrations/
└── public/
\`\`\`

**Key Features:**
- Database migrations
- Custom routes and views
- UI component integration
- Event system integration

**Troubleshooting Addons:**
- Check server logs for loading errors
- Verify package.json syntax
- Ensure entry point exists
- Check addon permissions`,
      keywords: ['addon', 'plugin', 'extension', 'package.json', 'migration', 'custom'],
      relatedTopics: ['Development', 'Database', 'UI Components']
    }
  ];

  private errorPatterns: ErrorPattern[] = [
    {
      pattern: /ECONNREFUSED.*3306|ECONNREFUSED.*5432/i,
      type: 'Database Connection',
      category: 'Database',
      commonCauses: [
        'Database server not running',
        'Incorrect connection credentials',
        'Wrong host/port configuration',
        'Firewall blocking connection'
      ],
      solutions: [
        'Verify database server is running',
        'Check DATABASE_URL in .env file',
        'Test database connection manually',
        'Check firewall settings'
      ]
    },
    {
      pattern: /EADDRINUSE.*3001|port.*already in use/i,
      type: 'Port Conflict',
      category: 'Network',
      commonCauses: [
        'Another process using port 3001',
        'Previous Airlink instance still running',
        'Port specified in .env already taken'
      ],
      solutions: [
        'Kill existing process: `pkill -f airlink`',
        'Change PORT in .env file',
        'Find process using port: `lsof -i :3001`',
        'Use different port number'
      ]
    },
    {
      pattern: /Cannot connect to the Docker daemon/i,
      type: 'Docker Connection',
      category: 'Docker',
      commonCauses: [
        'Docker service not running',
        'User not in docker group',
        'Docker socket permissions',
        'Docker not installed'
      ],
      solutions: [
        'Start Docker: `sudo systemctl start docker`',
        'Add user to docker group: `sudo usermod -aG docker $USER`',
        'Check Docker status: `sudo systemctl status docker`',
        'Install Docker if missing'
      ]
    },
    {
      pattern: /npm ERR!.*EACCES.*permission denied/i,
      type: 'NPM Permission Error',
      category: 'Installation',
      commonCauses: [
        'Running npm with insufficient permissions',
        'npm installed with sudo',
        'Incorrect npm prefix configuration'
      ],
      solutions: [
        'Use Node Version Manager (nvm) instead of system Node.js',
        'Configure npm prefix: `npm config set prefix ~/.npm-global`',
        'Fix npm permissions: `sudo chown -R $(whoami) ~/.npm`',
        'Avoid using sudo with npm install'
      ]
    },
    {
      pattern: /Error: Cannot find module|MODULE_NOT_FOUND/i,
      type: 'Missing Dependencies',
      category: 'Installation',
      commonCauses: [
        'Dependencies not installed',
        'Corrupted node_modules',
        'Version mismatch',
        'Missing TypeScript compilation'
      ],
      solutions: [
        'Run `npm install` to install dependencies',
        'Delete node_modules and reinstall: `rm -rf node_modules && npm install`',
        'Build the project: `npm run build`',
        'Check package.json for correct dependencies'
      ]
    },
    {
      pattern: /ENOTFOUND.*getaddrinfo/i,
      type: 'DNS/Network Error',
      category: 'Network',
      commonCauses: [
        'DNS resolution failure',
        'Network connectivity issues',
        'Incorrect hostname/URL',
        'Firewall blocking requests'
      ],
      solutions: [
        'Check internet connectivity',
        'Verify hostname/URL spelling',
        'Try different DNS servers (*******, *******)',
        'Check firewall settings'
      ]
    },
    {
      pattern: /Prisma.*migration.*failed/i,
      type: 'Database Migration Error',
      category: 'Database',
      commonCauses: [
        'Database schema conflicts',
        'Insufficient database permissions',
        'Database connection issues',
        'Corrupted migration files'
      ],
      solutions: [
        'Reset database: `npm run migrate:reset`',
        'Check database permissions',
        'Verify DATABASE_URL in .env',
        'Run migrations manually: `npm run migrate:dev`'
      ]
    }
  ];

  constructor() {
    this.simpleKB = new SimpleKnowledgeBase();
  }

  searchKnowledgeBase(query: string): string | null {
    const result = this.simpleKB.searchProblem(query);
    if (result) {
      return `**Problem:** ${result.problem}\n**Solution:** ${result.solution}`;
    }
    return null;
  }

  addToKnowledgeBase(problem: string, keywords: string[], solution: string): void {
    this.simpleKB.addEntry(problem, keywords, solution);
    logger.info(`Added new entry to knowledge base: ${problem}`);
  }

  getKnowledgeBaseStats(): { count: number; problems: string[] } {
    return {
      count: this.simpleKB.getEntriesCount(),
      problems: this.simpleKB.getAllProblems()
    };
  }

  reloadKnowledgeBase(): void {
    this.simpleKB.reloadKnowledgeBase();
  }

  getSystemPrompt(): string {
    return `You are an expert support assistant for Airlink Panel, an open-source game server management platform. 

**About Airlink Panel:**
- Advanced game server management platform built with Node.js, TypeScript, and Docker
- Uses Prisma ORM with PostgreSQL/MySQL databases
- Features a powerful addon system for extensibility
- Manages game servers through Docker containers via the Airlink Daemon (airlinkd)
- Supports multiple nodes for distributed server management

**Key Components:**
- **Panel**: Web interface for server management (default port 3001)
- **Daemon (airlinkd)**: Runs on nodes to manage Docker containers
- **Database**: Stores configuration, users, servers, and logs
- **Addons**: Extend functionality with custom features

**Common User Issues:**
1. Installation problems (Node.js, npm, dependencies)
2. Database connection errors
3. Docker/daemon connectivity issues
4. Port conflicts
5. Permission problems
6. Configuration errors

**Your Role:**
- Provide accurate, helpful troubleshooting advice
- Reference official documentation when possible
- Ask clarifying questions when needed
- Suggest step-by-step solutions
- Be patient and understanding with users of all skill levels

**Response Style:**
- Keep responses SHORT (2-3 sentences max for simple questions)
- Use bullet points instead of long paragraphs
- Get straight to the solution - no lengthy explanations
- Use code blocks for commands only
- Ask ONE clarifying question if needed, not multiple
- Use minimal emojis (max 1-2 per response)
- For complex issues, provide only the most essential steps`;
  }

  searchKnowledge(query: string): AirlinkKnowledge[] {
    const searchTerms = query.toLowerCase().split(' ');
    
    return this.knowledge.filter(item => {
      const searchableText = `${item.title} ${item.content} ${item.keywords.join(' ')}`.toLowerCase();
      return searchTerms.some(term => searchableText.includes(term));
    }).sort((a, b) => {
      // Sort by relevance (number of matching terms)
      const aMatches = searchTerms.filter(term => 
        `${a.title} ${a.content} ${a.keywords.join(' ')}`.toLowerCase().includes(term)
      ).length;
      const bMatches = searchTerms.filter(term => 
        `${b.title} ${b.content} ${b.keywords.join(' ')}`.toLowerCase().includes(term)
      ).length;
      return bMatches - aMatches;
    });
  }

  analyzeError(errorText: string): ErrorPattern | null {
    for (const pattern of this.errorPatterns) {
      if (pattern.pattern.test(errorText)) {
        return pattern;
      }
    }
    return null;
  }

  getAllCategories(): string[] {
    return [...new Set(this.knowledge.map(item => item.category))];
  }

  getKnowledgeByCategory(category: string): AirlinkKnowledge[] {
    return this.knowledge.filter(item => item.category === category);
  }
}
