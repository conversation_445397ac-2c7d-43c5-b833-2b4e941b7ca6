import { ConversationContext, AIMessage } from '../types';
import { config } from '../utils/config';
import logger from '../utils/logger';

export class ConversationManager {
  private conversations: Map<string, ConversationContext> = new Map();
  private cleanupInterval: NodeJS.Timeout;

  constructor() {
    // Clean up old conversations every 5 minutes
    this.cleanupInterval = setInterval(() => {
      this.cleanupExpiredConversations();
    }, 5 * 60 * 1000);
  }

  getConversationKey(userId: string, channelId: string): string {
    return `${userId}-${channelId}`;
  }

  getOrCreateConversation(userId: string, channelId: string): ConversationContext {
    const key = this.getConversationKey(userId, channelId);
    let conversation = this.conversations.get(key);

    if (!conversation) {
      conversation = {
        userId,
        channelId,
        messages: [],
        lastActivity: new Date()
      };
      this.conversations.set(key, conversation);
      logger.info(`Created new conversation for user ${userId} in channel ${channelId}`);
    }

    return conversation;
  }

  addMessage(userId: string, channelId: string, message: AIMessage): void {
    const conversation = this.getOrCreateConversation(userId, channelId);
    conversation.messages.push(message);
    conversation.lastActivity = new Date();

    // Trim conversation history if it exceeds the limit
    if (conversation.messages.length > config.maxConversationHistory) {
      // Keep system message and trim user/assistant messages
      const systemMessages = conversation.messages.filter(m => m.role === 'system');
      const otherMessages = conversation.messages.filter(m => m.role !== 'system');
      
      // Keep the most recent messages
      const trimmedOtherMessages = otherMessages.slice(-config.maxConversationHistory + systemMessages.length);
      conversation.messages = [...systemMessages, ...trimmedOtherMessages];
    }

    logger.debug(`Added message to conversation ${userId}-${channelId}. Total messages: ${conversation.messages.length}`);
  }

  getConversationHistory(userId: string, channelId: string): AIMessage[] {
    const conversation = this.conversations.get(this.getConversationKey(userId, channelId));
    return conversation?.messages || [];
  }

  updateConversationTopic(userId: string, channelId: string, topic: string): void {
    const conversation = this.getOrCreateConversation(userId, channelId);
    conversation.topic = topic;
    conversation.lastActivity = new Date();
  }

  clearConversation(userId: string, channelId: string): void {
    const key = this.getConversationKey(userId, channelId);
    this.conversations.delete(key);
    logger.info(`Cleared conversation for user ${userId} in channel ${channelId}`);
  }

  getActiveConversationsCount(): number {
    return this.conversations.size;
  }

  getConversationStats(): { total: number; byChannel: Record<string, number> } {
    const byChannel: Record<string, number> = {};
    
    for (const conversation of this.conversations.values()) {
      byChannel[conversation.channelId] = (byChannel[conversation.channelId] || 0) + 1;
    }

    return {
      total: this.conversations.size,
      byChannel
    };
  }

  private cleanupExpiredConversations(): void {
    const now = new Date();
    const timeoutMs = config.conversationTimeoutMinutes * 60 * 1000;
    let cleanedCount = 0;

    for (const [key, conversation] of this.conversations.entries()) {
      const timeSinceLastActivity = now.getTime() - conversation.lastActivity.getTime();
      
      if (timeSinceLastActivity > timeoutMs) {
        this.conversations.delete(key);
        cleanedCount++;
      }
    }

    if (cleanedCount > 0) {
      logger.info(`Cleaned up ${cleanedCount} expired conversations`);
    }
  }

  destroy(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }
    this.conversations.clear();
  }
}
