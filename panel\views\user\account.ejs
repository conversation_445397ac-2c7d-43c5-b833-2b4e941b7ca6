<%- include('../components/header', { title: req.translations.userAccountTitle || 'Account' }) %>

<!-- Toast Container -->
<div id="toast-container" class="fixed top-4 right-4 z-50 flex flex-col gap-2"></div>

<main class="h-screen m-auto">
   <div class="flex h-screen">
     <!-- Sidebar -->
     <div class="w-60 h-full">
       <%- include('../components/template') %>
     </div>
     <!-- Content -->
     <div class="flex-1 p-6 overflow-y-auto pt-16">
      <!-- Page Header -->
      <div class="sm:flex sm:items-center px-8 pt-4">
        <div class="sm:flex-auto">
          <div class="flex items-center gap-3">
            <div>
              <h1 class="text-lg font-medium leading-6 text-neutral-800 dark:text-white"><%= req.translations.userAccountTitle %></h1>
              <p class="mt-1 tracking-tight text-sm text-neutral-500"><%= req.translations.userAccountText %></p>
            </div>
          </div>
        </div>
      </div>

      <div class="px-4 sm:px-6 lg:px-8">
        <dl class="mt-5 grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-2">
           <!-- Account Settings Card -->
           <div class="overflow-hidden rounded-xl bg-neutral-800/20 border border-neutral-700/20 shadow-md hover:shadow-lg transition-all duration-300 p-6">
              <div class="flex items-center justify-between mb-4">
                <dt class="truncate text-sm font-medium text-white"><%= req.translations.updateAccount %></dt>
                <div class="rounded-lg bg-neutral-700/20 p-2">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-neutral-400" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1.5">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                  </svg>
                </div>
              </div>

              <div class="min-w-0 flex-1">
                <div class="flex flex-col sm:flex-row justify-between mt-5">
                  <!-- Username Form -->
                  <div class="w-full sm:w-1/2 sm:mr-4 mb-4 sm:mb-0">
                    <form id="change-username-form" action="/change-username" method="POST">
                      <%- include('../components/csrf') %>
                      <label class="block text-neutral-400 text-sm font-medium mb-2"><%= req.translations.username %>:</label>
                      <div class="flex space-x-2">
                        <input id="username" type="text" name="username"
                          class="rounded-xl focus:ring-1 focus:ring-neutral-500/30 text-sm mt-1 mb-0 w-full transition-all hover:bg-white/5 px-4 py-2 border-neutral-600/30 bg-neutral-700/20 placeholder-neutral-400 text-white border"
                          placeholder="<%= user.username %>" />
                        <button type="submit" id="change-username-btn" disabled
                          class="mt-1 rounded-xl bg-white hover:bg-neutral-200 text-neutral-800 px-3 py-2 text-center text-sm font-medium shadow-lg transition focus:outline focus:outline-2 focus:outline-offset-2 disabled:opacity-50 disabled:cursor-not-allowed">
                          <%= req.translations.update %>
                        </button>
                      </div>
                      <span id="username-feedback" class="mt-2 border border-neutral-600/30 text-neutral-400 text-xs font-medium mr-2 px-2.5 py-1 rounded-full inline-block min-w-max transition-opacity duration-300 ease-in-out"><%= req.translations.checking %></span>
                    </form>
                  </div>

                  <!-- Email Form -->
                  <div class="w-full sm:w-1/2">
                    <form id="change-email-form">
                      <%- include('../components/csrf') %>
                      <label class="block text-neutral-400 text-sm font-medium mb-2"><%= req.translations.email %>:</label>
                      <div class="flex space-x-2">
                        <input id="email" type="email" name="email"
                          class="rounded-xl focus:ring-1 focus:ring-neutral-500/30 text-sm mt-1 mb-0 w-full transition-all hover:bg-white/5 px-4 py-2 border-neutral-600/30 bg-neutral-700/20 placeholder-neutral-400 text-white border"
                          placeholder="<%= user.email %>" />
                        <button type="submit" id="change-email-btn"
                          class="mt-1 rounded-xl bg-white hover:bg-neutral-200 text-neutral-800 px-3 py-2 text-center text-sm font-medium shadow-lg transition focus:outline focus:outline-2 focus:outline-offset-2">
                          <%= req.translations.update %>
                        </button>
                      </div>
                    </form>
                  </div>
                </div>

                <!-- Description Form -->
                <form id="change-description-form" class="mt-6 p-4 rounded-xl bg-neutral-700/10 border border-neutral-600/20">
                  <%- include('../components/csrf') %>
                  <label class="block text-neutral-400 text-sm font-medium mb-2"><%= req.translations.description %>:</label>
                  <textarea id="description" name="description"
                    class="rounded-xl focus:ring-1 focus:ring-neutral-500/30 text-sm mt-1 mb-3 w-full transition-all hover:bg-white/5 px-4 py-2 border-neutral-600/30 bg-neutral-700/20 placeholder-neutral-400 text-white border min-h-[80px]"
                    placeholder="<%= user.description %>"><%= user.description %></textarea>
                  <button type="submit" id="change-description-btn"
                    class="block rounded-xl bg-white hover:bg-neutral-200 text-neutral-800 px-3 py-2 text-center text-sm font-medium shadow-lg transition focus:outline focus:outline-2 focus:outline-offset-2">
                    <%= req.translations.updateDescription %>
                  </button>
                </form>

                <!-- Password Form -->
                <form id="change-password-form" class="mt-6 p-4 rounded-xl bg-neutral-700/10 border border-neutral-600/20">
                  <%- include('../components/csrf') %>
                  <h3 class="text-sm font-medium text-white mb-3"><%= req.translations.changePassword || 'Change Password' %></h3>

                  <label class="block text-neutral-400 text-sm font-medium mb-2"><%= req.translations.currentPassword %>:</label>
                  <div id="current-password-feedback" class="border border-neutral-600/30 text-neutral-400 text-xs mr-2 px-2.5 py-1 rounded-full inline-block min-w-max transition-opacity duration-300 ease-in-out mb-2"><%= req.translations.checking %></div>
                  <input id="currentPassword" type="password" name="currentPassword"
                    class="rounded-xl focus:ring-1 focus:ring-neutral-500/30 text-sm mt-1 mb-4 w-full transition-all hover:bg-white/5 px-4 py-2 border-neutral-600/30 bg-neutral-700/20 placeholder-neutral-400 text-white border"
                    placeholder="<%= req.translations.currentPasswordHide %>" />

                  <label class="block text-neutral-400 text-sm font-medium mb-2"><%= req.translations.newPassword %>:</label>
                  <input id="newPassword" type="password" name="newPassword"
                    class="rounded-xl focus:ring-1 focus:ring-neutral-500/30 text-sm mt-1 mb-4 w-full transition-all hover:bg-white/5 px-4 py-2 border-neutral-600/30 bg-neutral-700/20 placeholder-neutral-400 text-white border"
                    placeholder="<%= req.translations.newPasswordHide %>" disabled />

                  <button type="submit"
                    class="block rounded-xl bg-white hover:bg-neutral-200 text-neutral-800 px-3 py-2 text-center text-sm font-medium shadow-lg transition focus:outline focus:outline-2 focus:outline-offset-2">
                    <%= req.translations.update_password %>
                  </button>
                </form>

                <!-- 2FA Section -->
                <div class="mt-6 p-4 rounded-xl bg-neutral-700/10 border border-neutral-600/20">
                  <h3 class="text-sm font-medium text-white mb-3"><%= req.translations.twoFactorAuth || 'Two-Factor Authentication' %></h3>
                  <% if (user['2fa'] !== true) { %>
                    <p class="text-sm text-neutral-400">2FA was in development, and so it is not available.</p>
                  <% } else { %>
                    <form action="/disable-2fa" method="POST">
                      <%- include('../components/csrf') %>
                      <button class="block rounded-xl bg-white hover:bg-neutral-200 text-neutral-800 px-3 py-2 text-center text-sm font-medium shadow-lg transition focus:outline focus:outline-2 focus:outline-offset-2">
                        <%= req.translations.disable2FA %>
                      </button>
                    </form>
                  <% } %>
                </div>
              </div>
           </div>

           <!-- Language Preferences Card -->
           <div class="overflow-hidden rounded-xl bg-neutral-800/20 border border-neutral-700/20 shadow-md hover:shadow-lg transition-all duration-300 p-6">
              <div class="flex items-center justify-between mb-4">
                <dt class="truncate text-sm font-medium text-white"><%= req.translations.languagePreference || 'Language Preferences' %></dt>
                <div class="rounded-lg bg-neutral-700/20 p-2">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-neutral-400" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1.5">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129" />
                  </svg>
                </div>
              </div>

              <div class="min-w-0 flex-1">
                <form id="language-preference-form" class="p-4 rounded-xl bg-neutral-700/10 border border-neutral-600/20">
                  <%- include('../components/csrf') %>
                  <label class="block text-neutral-400 text-sm font-medium mb-2"><%= req.translations.language || 'Language' %>:</label>
                  <select id="language" name="language"
                    class="rounded-xl focus:ring-1 focus:ring-neutral-500/30 text-sm mt-1 mb-4 w-full transition-all hover:bg-white/5 px-4 py-2 border-neutral-600/30 bg-neutral-700/20 placeholder-neutral-400 text-white border">
                    <option value="en" <%= req.lang === 'en' ? 'selected' : '' %>>English</option>
                    <option value="fr" <%= req.lang === 'fr' ? 'selected' : '' %>>Français</option>
                  </select>
                  <button type="submit"
                    class="block rounded-xl bg-white hover:bg-neutral-200 text-neutral-800 px-3 py-2 text-center text-sm font-medium shadow-lg transition focus:outline focus:outline-2 focus:outline-offset-2">
                    <%= req.translations.saveLanguagePreference || 'Save Language Preference' %>
                  </button>
                </form>
              </div>
           </div>

           <!-- Login History Card -->
           <div class="overflow-hidden rounded-xl bg-neutral-800/20 border border-neutral-700/20 shadow-md hover:shadow-lg transition-all duration-300 p-6 col-span-1 sm:col-span-2">
              <div class="flex items-center justify-between mb-4">
                <dt class="truncate text-sm font-medium text-white"><%= req.translations.loginHistory || 'Login History' %></dt>
                <div class="rounded-lg bg-neutral-700/20 p-2">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-neutral-400" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="1.5">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
              </div>

              <div class="min-w-0 flex-1">
                <div class="overflow-x-auto p-4 rounded-xl bg-neutral-700/10 border border-neutral-600/20">
                  <table class="min-w-full divide-y divide-neutral-600/20">
                    <thead>
                      <tr>
                        <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-white sm:pl-0"><%= req.translations.dateTime || 'Date & Time' %></th>
                        <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-white"><%= req.translations.ipAddress || 'IP Address' %></th>
                        <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-white"><%= req.translations.browserDevice || 'Browser/Device' %></th>
                      </tr>
                    </thead>
                    <tbody class="divide-y divide-neutral-600/20">
                      <% if (loginHistory && loginHistory.length > 0) { %>
                        <% loginHistory.forEach(login => { %>
                          <tr class="hover:bg-neutral-700/10 transition-colors">
                            <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm text-white sm:pl-0">
                              <%= new Date(login.timestamp).toLocaleString() %>
                            </td>
                            <td class="whitespace-nowrap px-3 py-4 text-sm text-neutral-400">
                              <%= login.ipAddress || (req.translations.unknown || 'Unknown') %>
                            </td>
                            <td class="whitespace-nowrap px-3 py-4 text-sm text-neutral-400 truncate max-w-[200px]">
                              <%= login.userAgent || (req.translations.unknown || 'Unknown') %>
                            </td>
                          </tr>
                        <% }) %>
                      <% } else { %>
                        <tr>
                          <td colspan="3" class="whitespace-nowrap py-4 pl-4 pr-3 text-sm text-neutral-400 sm:pl-0 text-center">
                            <%= req.translations.noLoginHistory || 'No login history available' %>
                          </td>
                        </tr>
                      <% } %>
                    </tbody>
                  </table>
                </div>
              </div>
           </div>
        </dl>
      </div>
   </div>
   </div>
</main>

<%- include('../components/toast')%>

<script>
  // Form Handlers
  document.addEventListener('DOMContentLoaded', () => {
    const { showToast } = createToastSystem();
    let isSubmitting = false;

    // Generic form handler
    const submitForm = async (endpoint, data, successMessage, redirectUrl = null) => {
      if (isSubmitting) return;
      isSubmitting = true;

      // Get CSRF token from meta tag
      const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');

      try {
        const response = await fetch(endpoint, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'CSRF-Token': csrfToken
          },
          body: JSON.stringify(data)
        });

        const responseBody = await response.text();
        if (response.ok) {
          showToast(successMessage, 'success');
          if (redirectUrl) {
            setTimeout(() => window.location.href = redirectUrl, 1500);
          }
        } else {
          showToast(responseBody);
        }
      } catch (error) {
        showToast(`Error: ${error.message}`);
      } finally {
        isSubmitting = false;
      }
    };

    // Username Form
    document.getElementById('change-username-form')?.addEventListener('submit', async (event) => {
      event.preventDefault();

      const usernameInput = event.target.querySelector('#username');
      const newUsername = usernameInput.value.trim();

      await submitForm(
        '/update-username',
        { newUsername: newUsername },
        'Username updated successfully!',
      );

      const sidebarUsername = document.getElementById('sidebar-username');
      sidebarUsername.textContent = newUsername;

      usernameInput.placeholder = newUsername;
    });


    // Email Form
    document.getElementById('change-email-form')?.addEventListener('submit', async (event) => {
      event.preventDefault();
      const email = event.target.querySelector('#email').value.trim();
      await submitForm(
        '/change-email',
        { email },
        'Email updated successfully!',
      );
    });

    // Description Form
    document.getElementById('change-description-form')?.addEventListener('submit', async (event) => {
      event.preventDefault();
       const descriptionEl = event.target.querySelector('#description');
       const description = descriptionEl.value.trim();

       await submitForm(
         '/update-description',
         { description },
         'Description updated successfully!'
       );

       const sidebarDescription = document.getElementById('sidebar-description');
       sidebarDescription.textContent = description;

       descriptionEl.placeholder = description;
    });

    // Password Form
    document.getElementById('change-password-form')?.addEventListener('submit', async (event) => {
      event.preventDefault();
      const form = event.target;
      const currentPassword = form.querySelector('#currentPassword').value;
      const newPassword = form.querySelector('#newPassword').value;

      await submitForm(
        '/change-password',
        { currentPassword, newPassword },
        'Password updated successfully!',
        '/login?err=UpdatedCredentials'
      );
    });

    // Username Validation
    const usernameInput = document.getElementById('username');
    const changeUsernameBtn = document.getElementById('change-username-btn');
    const usernameFeedback = document.getElementById('username-feedback');
    let usernameTimeoutId;

    async function checkUsername() {
      const username = usernameInput.value.trim().toLowerCase();

      if (!username) {
        usernameFeedback.style.display = 'none';
        changeUsernameBtn.disabled = true;
        return;
      }

      usernameFeedback.style.display = 'inline-block';
      showLoadingAnimation(usernameFeedback);

      try {
        const response = await fetch(`/check-username?username=${encodeURIComponent(username)}`);
        const data = await response.json();

        usernameFeedback.innerHTML = `<span id="username-status">
          ${data.exists ? '<%= req.translations.usernameTaken %>' : '<%= req.translations.usernameIsNotTaken %>'}
        </span>`;
        changeUsernameBtn.disabled = data.exists;
      } catch (error) {
        console.log('Error checking username:', error);
        usernameFeedback.innerHTML = `<span id="username-status"><%= req.translations.errorCheckingUsername || "Error checking username" %></span>`;
        changeUsernameBtn.disabled = true;
      }
    }

    usernameInput?.addEventListener('input', () => {
      clearTimeout(usernameTimeoutId);
      usernameTimeoutId = setTimeout(checkUsername, 500);
    });

    // Password Validation
    const currentPasswordInput = document.getElementById('currentPassword');
    const newPasswordInput = document.getElementById('newPassword');
    const currentPasswordFeedback = document.getElementById('current-password-feedback');
    let currentPasswordTimeoutId;
    let controller;

    function clearPasswordFeedback() {
      currentPasswordFeedback.innerHTML = '';
      currentPasswordFeedback.style.display = 'none';
      newPasswordInput.disabled = true;
      newPasswordInput.value = '';
    }

    async function validateCurrentPassword(currentPassword) {
      if (!currentPassword) {
        clearPasswordFeedback();
        return;
      }

      try {
        // Get CSRF token from meta tag
        const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');

        const response = await fetch('/validate-password', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'CSRF-Token': csrfToken
          },
          body: JSON.stringify({ currentPassword })
        });

        if (response.ok) {
          const data = await response.json();
          if (data.valid) {
            currentPasswordFeedback.innerHTML = '<%= req.translations.correctPassword %>';
            newPasswordInput.disabled = false;
          } else {
            currentPasswordFeedback.innerHTML = '<%= req.translations.incorrectPassword %>';
            newPasswordInput.disabled = true;
          }
          currentPasswordFeedback.style.display = 'inline-block';
        } else {
          throw new Error('Failed to validate password');
        }
      } catch (error) {
        console.error('Error validating password:', error);
        currentPasswordFeedback.innerHTML = '<%= req.translations.validatingPasswordError %>';
        currentPasswordFeedback.style.display = 'inline-block';
      }
    }

    currentPasswordInput?.addEventListener('input', () => {
      clearTimeout(currentPasswordTimeoutId);
      if (controller) {
        controller.abort();
      }

      controller = new AbortController();
      const currentPassword = currentPasswordInput.value.trim();

      currentPasswordTimeoutId = setTimeout(() => {
        validateCurrentPassword(currentPassword);
      }, 500);
    });

    // Language Preference Form
    document.getElementById('language-preference-form')?.addEventListener('submit', async (event) => {
      event.preventDefault();
      const languageSelect = event.target.querySelector('#language');
      const language = languageSelect.value;

      try {
        // Get CSRF token from meta tag
        const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');

        const response = await fetch('/set-language', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'CSRF-Token': csrfToken
          },
          body: JSON.stringify({ language })
        });

        if (response.ok) {
          // Show message in the selected language
          const successMessage = language === 'fr'
            ? 'Préférence de langue enregistrée. Actualisation de la page...'
            : 'Language preference saved. Refreshing page...';

          showToast(successMessage, 'success');
          setTimeout(() => window.location.reload(), 1500);
        } else {
          const errorText = await response.text();
          const errorPrefix = language === 'fr' ? 'Erreur' : 'Error';
          showToast(`${errorPrefix}: ${errorText}`, 'error');
        }
      } catch (error) {
        const errorPrefix = language === 'fr' ? 'Erreur' : 'Error';
        showToast(`${errorPrefix}: ${error.message}`, 'error');
      }
    });

    // Loading Animation
    function showLoadingAnimation(feedbackElement) {
      if (!feedbackElement) return;
      feedbackElement.innerHTML = `
        <span id="status">
          <svg class="animate-spin h-3 w-3 inline-flex mr-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          <span id="feedback-message">Checking...</span>
        </span>
      `;
    }
  });
  </script>
<%- include('../components/footer') %>
