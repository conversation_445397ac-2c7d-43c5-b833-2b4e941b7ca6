# Airlink Discord Support Bot

An AI-powered Discord support bot designed specifically for the Airlink Panel community. This bot provides intelligent assistance for Airlink setup, troubleshooting, and general support using GPT-4o mini.

## Features

- 🤖 **AI-Powered Responses**: Uses GPT-4o mini for intelligent, context-aware support
- 🖼️ **Image Analysis**: Extracts text from error screenshots and provides contextual help
- 💬 **Conversation Memory**: Maintains conversation context for better assistance
- 📚 **Airlink Knowledge Base**: Comprehensive knowledge about Airlink Panel setup and troubleshooting
- 🔧 **Error Detection**: Automatically detects common Airlink issues from user messages and images
- 📝 **Logging**: Comprehensive logging for monitoring and debugging
- 📚 **Knowledge Base**: Simple text file-based problem/solution database

## Prerequisites

- Node.js 18.0.0 or higher
- Discord Bot Token
- Custom AI API endpoint with GPT-4o mini access

## Quick Start

### Option 1: Automated Setup (Recommended)
1. Run the setup script:
   ```bash
   node setup.js
   ```
2. Follow the prompts to configure your bot
3. Install dependencies and start:
   ```bash
   npm install
   npm run build
   npm start
   ```

### Option 2: Windows Quick Start
1. Double-click `start.bat` - it will handle everything automatically!

### Option 3: Manual Setup
1. Install dependencies:
   ```bash
   npm install
   ```

2. Copy and configure environment:
   ```bash
   cp .env.example .env
   ```

3. Edit `.env` with your Discord bot credentials:
   - `DISCORD_TOKEN`: Your Discord bot token
   - `DISCORD_CLIENT_ID`: Your Discord application client ID
   - AI settings are pre-configured with your custom API

4. Build and start:
   ```bash
   npm run build
   npm start
   ```

For development:
```bash
npm run dev
```

## Discord Bot Setup

### Step 1: Create Discord Application
1. Go to the [Discord Developer Portal](https://discord.com/developers/applications)
2. Click "New Application" and give it a name (e.g., "Airlink Support Bot")
3. Go to the "Bot" section in the left sidebar
4. Click "Add Bot" to create a bot user
5. Copy the bot token (you'll need this for setup)

### Step 2: Configure Bot Permissions
In the "Bot" section, enable these permissions:
- **Text Permissions:**
  - Send Messages
  - Send Messages in Threads
  - Embed Links
  - Attach Files
  - Read Message History
  - Use External Emojis
  - Add Reactions

- **General Permissions:**
  - Read Messages/View Channels

### Step 3: Generate Invite Link
1. Go to "OAuth2" → "URL Generator"
2. Select "bot" scope
3. Select the permissions listed above
4. Copy the generated URL and use it to invite the bot to your server

### Step 4: Get Client ID
1. Go to "General Information"
2. Copy the "Application ID" (this is your Client ID)

### Step 5: Configure Bot
Use the setup script or manually add these values to your `.env` file:
- `DISCORD_TOKEN`: The bot token from Step 1
- `DISCORD_CLIENT_ID`: The Application ID from Step 4

## Usage

The bot responds to:
- Direct messages
- Mentions in channels (@BotName)
- Messages starting with ! (configurable prefix)
- **Mentions in a dedicated support channel** (if configured)
- Messages in channels with "support" in the name
- Image uploads (for error analysis)

### Example Interactions

- "How do I install Airlink Panel?"
- "I'm getting a Docker error when starting my server"
- *Upload screenshot of error* - Bot will analyze and provide help
- "My node isn't connecting to the panel"

## Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `DISCORD_TOKEN` | Discord bot token | Required |
| `DISCORD_CLIENT_ID` | Discord application client ID | Required |
| `DEDICATED_SUPPORT_CHANNEL_ID` | Channel ID where bot responds to ALL messages | Optional |
| `AI_API_URL` | Custom AI API endpoint | Required |
| `AI_API_KEY` | AI API key | Required |
| `AI_MODEL` | AI model to use | `gpt-4o-mini` |
| `BOT_PREFIX` | Command prefix | `!` |
| `MAX_CONVERSATION_HISTORY` | Max messages to remember | `10` |
| `CONVERSATION_TIMEOUT_MINUTES` | Conversation timeout | `30` |
| `LOG_LEVEL` | Logging level | `info` |

## 🛠️ Commands

### User Commands
- Direct messages and mentions work automatically
- Upload images for error analysis
- Natural conversation - no special commands needed

### Admin Commands (Server Administrators Only)
- `!kb status` - Show knowledge base statistics
- `!kb search <query>` - Search knowledge base
- `!kb add <problem>\n<keywords>\n<solution>` - Add entry to knowledge base
- `!kb reload` - Reload knowledge base from file

## Development

```bash
# Install dependencies
npm install

# Run in development mode
npm run dev

# Build for production
npm run build

# Run linting
npm run lint

# Format code
npm run format
```

## Support

For issues with the bot itself, please check the logs in the `logs/` directory or contact the Airlink development team.

## License

MIT License - see LICENSE file for details.
