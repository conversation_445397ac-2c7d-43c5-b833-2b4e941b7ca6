AIRLINK PANEL KNOWLEDGE BASE

=== INSTALLATION ISSUES ===

PROBLEM: Node.js version error
KEYWORDS: node version, nodejs, npm version
SOLUTION: Airlink requires Node.js v16+ (v18+ recommended). Check version with `node --version`. Update Node.js if needed.

PROBLEM: npm install fails
KEYWORDS: npm install, npm error, dependencies
SOLUTION: Clear npm cache with `npm cache clean --force`, delete node_modules folder, then run `npm install` again.

PROBLEM: Permission denied during installation
KEYWORDS: permission denied, EACCES, sudo npm
SOLUTION: Don't use sudo with npm. Fix permissions with `sudo chown -R $(whoami) ~/.npm` or use nvm instead.

PROBLEM: Database connection failed
KEYWORDS: database connection, ECONNREFUSED, mysql, postgresql
SOLUTION: Check if database server is running. Verify DATABASE_URL in .env file. Test connection with `npm run migrate:dev`.

PROBLEM: Port already in use
KEYWORDS: port 3001, EADDRINUSE, port conflict
SOLUTION: Kill existing process with `pkill -f airlink` or change PORT in .env file to a different number.

=== DOCKER ISSUES ===

PROBLEM: Cannot connect to Docker daemon
KEYWORDS: docker daemon, docker connection, docker not running
SOLUTION: Start Docker with `sudo systemctl start docker`. Add user to docker group: `sudo usermod -aG docker $USER`.

PROBLEM: Docker permission denied
KEYWORDS: docker permission, docker socket, permission denied docker
SOLUTION: Add user to docker group with `sudo usermod -aG docker $USER`, then logout and login again.

PROBLEM: Docker container won't start
KEYWORDS: container failed, docker start error, container exit
SOLUTION: Check Docker logs with `docker logs <container_name>`. Ensure sufficient disk space and memory.

=== DAEMON ISSUES ===

PROBLEM: Daemon not connecting to panel
KEYWORDS: daemon connection, airlinkd, node connection failed
SOLUTION: Check network connectivity between daemon and panel. Verify daemon configuration and API keys.

PROBLEM: Daemon installation failed
KEYWORDS: daemon install, airlinkd install, daemon setup
SOLUTION: Ensure Node.js v18+ is installed. Run `npm install` in daemon directory, then `npm run build`.

=== CONFIGURATION ISSUES ===

PROBLEM: Environment variables not working
KEYWORDS: env file, environment variables, config not loading
SOLUTION: Ensure .env file is in root directory. Check file permissions. Restart the panel after changes.

PROBLEM: Database migration failed
KEYWORDS: migration failed, prisma migration, database schema
SOLUTION: Reset database with `npm run migrate:reset` or run `npm run migrate:dev` to apply migrations.

PROBLEM: SSL certificate errors
KEYWORDS: ssl error, certificate, https
SOLUTION: Check certificate paths in .env file. Ensure certificates are valid and readable.

=== SERVER MANAGEMENT ===

PROBLEM: Server won't start
KEYWORDS: server start failed, game server, container start
SOLUTION: Check server logs in panel. Verify image configuration and resource limits.

PROBLEM: Server files missing
KEYWORDS: files missing, server files, file upload
SOLUTION: Re-upload server files through panel file manager. Check file permissions.

PROBLEM: Server console not working
KEYWORDS: console not working, server console, command not working
SOLUTION: Check daemon connection. Verify server is running. Try restarting the server.

=== NETWORK ISSUES ===

PROBLEM: Panel not accessible
KEYWORDS: panel not loading, can't access panel, website down
SOLUTION: Check if panel is running with `pm2 status` or `npm run start`. Verify port 3001 is open.

PROBLEM: Firewall blocking connections
KEYWORDS: firewall, connection blocked, port blocked
SOLUTION: Open required ports in firewall. For Ubuntu: `sudo ufw allow 3001`.

=== ADDON ISSUES ===

PROBLEM: Addon not loading
KEYWORDS: addon not working, plugin not loading, addon error
SOLUTION: Check addon package.json syntax. Verify entry point file exists. Check server logs for errors.

PROBLEM: Addon database migration failed
KEYWORDS: addon migration, addon database, migration error
SOLUTION: Check migration SQL syntax. Ensure database permissions. Try disabling and re-enabling addon.

=== GENERAL TROUBLESHOOTING ===

PROBLEM: Panel crashes frequently
KEYWORDS: panel crash, application crash, unexpected exit
SOLUTION: Check system resources (RAM, disk space). Review error logs. Update to latest version.

PROBLEM: Slow performance
KEYWORDS: slow panel, performance issues, lag
SOLUTION: Check system resources. Optimize database. Consider upgrading server hardware.

PROBLEM: Login issues
KEYWORDS: can't login, authentication failed, password wrong
SOLUTION: Reset password through database or create new admin user with `npm run create:admin`.

PROBLEM: File upload not working
KEYWORDS: file upload, upload failed, file manager
SOLUTION: Check file size limits. Verify disk space. Check file permissions on upload directory.

PROBLEM: Backup and restore
KEYWORDS: backup, restore, data backup
SOLUTION: Backup database with `mysqldump` or `pg_dump`. Copy panel files. Use `npm run migrate:dev` after restore.
