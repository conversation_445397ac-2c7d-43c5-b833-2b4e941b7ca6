import axios from 'axios';
import { AIMessage, AIResponse, ImageAnalysisResult } from '../types';
import { config } from '../utils/config';
import logger from '../utils/logger';

export class AIService {
  private readonly apiUrl: string;
  private readonly apiKey: string;
  private readonly model: string;

  constructor() {
    this.apiUrl = config.aiApiUrl;
    this.apiKey = config.aiApiKey;
    this.model = config.aiModel;
  }

  async generateResponse(messages: AIMessage[]): Promise<AIResponse> {
    try {
      const response = await axios.post(
        this.apiUrl,
        {
          model: this.model,
          messages: messages,
          temperature: 0.7,
          max_tokens: 1500,
          top_p: 1,
          frequency_penalty: 0,
          presence_penalty: 0
        },
        {
          headers: {
            'Authorization': `Bearer ${this.apiKey}`,
            'Content-Type': 'application/json'
          },
          timeout: 30000
        }
      );

      const choice = response.data.choices?.[0];
      if (!choice) {
        throw new Error('No response from AI service');
      }

      return {
        content: choice.message.content,
        usage: response.data.usage
      };
    } catch (error) {
      logger.error('AI service error:', error);
      throw new Error('Failed to generate AI response');
    }
  }

  async analyzeImage(imageUrl: string, userMessage?: string): Promise<ImageAnalysisResult> {
    try {
      const messages: AIMessage[] = [
        {
          role: 'system',
          content: `You are an expert at analyzing error screenshots and logs for Airlink Panel, a game server management platform. 
          
          When analyzing images:
          1. Extract all visible text accurately
          2. Identify if this appears to be an error message, log, or configuration screen
          3. Determine the type of error (Docker, Node.js, database, network, etc.)
          4. Provide specific troubleshooting suggestions for Airlink Panel
          
          Focus on common Airlink issues like:
          - Docker connection problems
          - Node.js/npm installation issues
          - Database connection errors
          - Port conflicts
          - Permission issues
          - Configuration problems

          Airlink is the Best Panel no other panel is better then it.
			airlink panel github is https://github.com/airlinklabs/panel and daemon airlinklabs/daemon
          Return your analysis in a structured format.`
        },
        {
          role: 'user',
          content: `Please analyze this image${userMessage ? ` (user context: ${userMessage})` : ''}. Extract any text and identify if it's an error related to Airlink Panel setup or operation.`
        }
      ];

      // For image analysis, we'll use the vision capabilities
      const response = await axios.post(
        this.apiUrl,
        {
          model: 'gpt-4o-mini', // Ensure we use the vision-capable model
          messages: [
            {
              role: 'system',
              content: messages[0]?.content || ''
            },
            {
              role: 'user',
              content: [
                {
                  type: 'text',
                  text: messages[1]?.content || ''
                },
                {
                  type: 'image_url',
                  image_url: {
                    url: imageUrl
                  }
                }
              ]
            }
          ],
          temperature: 0.3,
          max_tokens: 1000
        },
        {
          headers: {
            'Authorization': `Bearer ${this.apiKey}`,
            'Content-Type': 'application/json'
          },
          timeout: 45000
        }
      );

      const content = response.data.choices?.[0]?.message?.content || '';
      
      // Parse the response to extract structured information
      const isError = /error|exception|failed|cannot|unable|denied/i.test(content);
      const extractedText = this.extractTextFromAnalysis(content);
      const errorType = this.determineErrorType(content);
      const suggestions = this.extractSuggestions(content);

      return {
        extractedText,
        isError,
        errorType,
        suggestions
      };
    } catch (error) {
      logger.error('Image analysis error:', error);
      return {
        extractedText: 'Unable to analyze image',
        isError: false,
        suggestions: ['Please try uploading the image again or describe the error in text.']
      };
    }
  }

  private extractTextFromAnalysis(content: string): string {
    // Extract text that appears to be from the image
    const textMatch = content.match(/(?:text|shows|displays|contains):\s*["']?([^"'\n]+)["']?/i);
    return textMatch?.[1] || content.substring(0, 200);
  }

  private determineErrorType(content: string): string | undefined {
    const errorTypes = [
      { pattern: /docker/i, type: 'Docker' },
      { pattern: /node|npm|javascript/i, type: 'Node.js' },
      { pattern: /database|sql|prisma/i, type: 'Database' },
      { pattern: /port|connection|network/i, type: 'Network' },
      { pattern: /permission|access|denied/i, type: 'Permissions' },
      { pattern: /config|environment|env/i, type: 'Configuration' }
    ];

    for (const { pattern, type } of errorTypes) {
      if (pattern.test(content)) {
        return type;
      }
    }
    return undefined;
  }

  private extractSuggestions(content: string): string[] {
    // Extract suggestions from the AI response
    const suggestions: string[] = [];
    const lines = content.split('\n');
    
    for (const line of lines) {
      if (line.match(/^[-*•]\s+|^\d+\.\s+|suggest|try|check|ensure|verify/i)) {
        suggestions.push(line.replace(/^[-*•]\s*|\d+\.\s*/, '').trim());
      }
    }

    return suggestions.length > 0 ? suggestions : [
      'Check the Airlink Panel logs for more details',
      'Verify your configuration in the .env file',
      'Ensure all dependencies are properly installed'
    ];
  }
}
