# Discord Bot Configuration
DISCORD_TOKEN=your_discord_bot_token_here
DISCORD_CLIENT_ID=your_discord_client_id_here
DEDICATED_SUPPORT_CHANNEL_ID=your_support_channel_id_here

# AI Configuration
AI_API_URL=your_custom_ai_api_url_here
AI_API_KEY=your_ai_api_key_here
AI_MODEL=gpt-4o-mini

# Bot Configuration
BOT_PREFIX=!
MAX_CONVERSATION_HISTORY=10
CONVERSATION_TIMEOUT_MINUTES=30

# Logging
LOG_LEVEL=info
LOG_FILE=logs/bot.log

# Optional: Database for persistent conversation storage
# DATABASE_URL=your_database_url_here

# Image processing settings
MAX_IMAGE_SIZE_MB=10
SUPPORTED_IMAGE_FORMATS=png,jpg,jpeg,gif,webp
